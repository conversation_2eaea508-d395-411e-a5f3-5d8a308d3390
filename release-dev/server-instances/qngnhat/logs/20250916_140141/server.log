2025-09-16T14:01:41.990+07:00  INFO 4931 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 4931 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-16T14:01:41.991+07:00  INFO 4931 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-16T14:01:42.732+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T14:01:42.815+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 79 ms. Found 22 JPA repository interfaces.
2025-09-16T14:01:42.826+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T14:01:42.828+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-16T14:01:42.829+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T14:01:42.837+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 10 JPA repository interfaces.
2025-09-16T14:01:42.838+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T14:01:42.902+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 63 ms. Found 3 JPA repository interfaces.
2025-09-16T14:01:42.927+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T14:01:42.935+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 1 JPA repository interface.
2025-09-16T14:01:42.945+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T14:01:42.948+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 2 JPA repository interfaces.
2025-09-16T14:01:42.948+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T14:01:42.952+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-09-16T14:01:42.955+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T14:01:42.959+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-09-16T14:01:42.964+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T14:01:42.967+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 3 JPA repository interfaces.
2025-09-16T14:01:42.968+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T14:01:42.969+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-16T14:01:42.969+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T14:01:42.976+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 10 JPA repository interfaces.
2025-09-16T14:01:42.983+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T14:01:42.986+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 3 JPA repository interfaces.
2025-09-16T14:01:42.990+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T14:01:42.994+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-16T14:01:42.995+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T14:01:43.005+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 12 JPA repository interfaces.
2025-09-16T14:01:43.006+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T14:01:43.009+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-09-16T14:01:43.009+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T14:01:43.010+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-16T14:01:43.010+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T14:01:43.011+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-16T14:01:43.011+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T14:01:43.015+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-09-16T14:01:43.015+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T14:01:43.017+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-16T14:01:43.017+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T14:01:43.017+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-16T14:01:43.017+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T14:01:43.028+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-09-16T14:01:43.038+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T14:01:43.045+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-09-16T14:01:43.045+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T14:01:43.049+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-09-16T14:01:43.049+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T14:01:43.054+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-09-16T14:01:43.054+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T14:01:43.059+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-09-16T14:01:43.059+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T14:01:43.064+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-09-16T14:01:43.065+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T14:01:43.073+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 11 JPA repository interfaces.
2025-09-16T14:01:43.073+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T14:01:43.083+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 14 JPA repository interfaces.
2025-09-16T14:01:43.083+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T14:01:43.098+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 15 ms. Found 24 JPA repository interfaces.
2025-09-16T14:01:43.099+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T14:01:43.100+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-16T14:01:43.106+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T14:01:43.107+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-16T14:01:43.107+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T14:01:43.114+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-09-16T14:01:43.116+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T14:01:43.155+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 38 ms. Found 66 JPA repository interfaces.
2025-09-16T14:01:43.155+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T14:01:43.157+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-16T14:01:43.161+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T14:01:43.165+07:00  INFO 4931 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-09-16T14:01:43.418+07:00  INFO 4931 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-16T14:01:43.425+07:00  INFO 4931 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-16T14:01:43.720+07:00  WARN 4931 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-16T14:01:43.952+07:00  INFO 4931 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-16T14:01:43.954+07:00  INFO 4931 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-16T14:01:43.966+07:00  INFO 4931 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-16T14:01:43.967+07:00  INFO 4931 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1869 ms
2025-09-16T14:01:44.031+07:00  WARN 4931 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-16T14:01:44.032+07:00  INFO 4931 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-16T14:01:44.162+07:00  INFO 4931 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@408f3054
2025-09-16T14:01:44.162+07:00  INFO 4931 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-16T14:01:44.168+07:00  WARN 4931 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-16T14:01:44.168+07:00  INFO 4931 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-16T14:01:44.173+07:00  INFO 4931 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@26c84006
2025-09-16T14:01:44.173+07:00  INFO 4931 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-16T14:01:44.173+07:00  WARN 4931 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-16T14:01:44.173+07:00  INFO 4931 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-16T14:01:44.182+07:00  INFO 4931 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@45fdd38d
2025-09-16T14:01:44.183+07:00  INFO 4931 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-16T14:01:44.183+07:00  WARN 4931 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-16T14:01:44.183+07:00  INFO 4931 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-16T14:01:44.192+07:00  INFO 4931 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@4b75d640
2025-09-16T14:01:44.192+07:00  INFO 4931 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-16T14:01:44.192+07:00  WARN 4931 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-16T14:01:44.192+07:00  INFO 4931 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-16T14:01:44.198+07:00  INFO 4931 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@3f7d20c5
2025-09-16T14:01:44.198+07:00  INFO 4931 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-16T14:01:44.198+07:00  INFO 4931 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-16T14:01:44.253+07:00  INFO 4931 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-16T14:01:44.255+07:00  INFO 4931 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@45144fdf{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.2580182593180378993/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@23c07745{STARTED}}
2025-09-16T14:01:44.255+07:00  INFO 4931 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@45144fdf{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.2580182593180378993/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@23c07745{STARTED}}
2025-09-16T14:01:44.256+07:00  INFO 4931 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@676c3745{STARTING}[12.0.15,sto=0] @3110ms
2025-09-16T14:01:44.407+07:00  INFO 4931 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-16T14:01:44.435+07:00  INFO 4931 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-16T14:01:44.450+07:00  INFO 4931 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-16T14:01:44.587+07:00  INFO 4931 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-16T14:01:44.624+07:00  WARN 4931 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-16T14:01:45.313+07:00  INFO 4931 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-16T14:01:45.322+07:00  INFO 4931 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@e2fecf8] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-16T14:01:45.479+07:00  INFO 4931 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-16T14:01:45.709+07:00  INFO 4931 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.groovy", "cloud.datatp.tms", "net.datatp.module.company.tmpl", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.chatbot", "net.datatp.module.core.print", "cloud.datatp.module.odoo", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-09-16T14:01:45.711+07:00  INFO 4931 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-16T14:01:45.717+07:00  INFO 4931 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-16T14:01:45.718+07:00  INFO 4931 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-16T14:01:45.744+07:00  INFO 4931 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-16T14:01:45.760+07:00  WARN 4931 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-16T14:01:47.794+07:00  INFO 4931 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-16T14:01:47.794+07:00  INFO 4931 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@139e4eb0] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-16T14:01:48.041+07:00  WARN 4931 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-16T14:01:48.041+07:00  WARN 4931 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-16T14:01:48.051+07:00  WARN 4931 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-16T14:01:48.051+07:00  WARN 4931 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-16T14:01:48.064+07:00  WARN 4931 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-16T14:01:48.064+07:00  WARN 4931 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-16T14:01:48.549+07:00  INFO 4931 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-16T14:01:48.555+07:00  INFO 4931 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-16T14:01:48.556+07:00  INFO 4931 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-16T14:01:48.577+07:00  INFO 4931 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-16T14:01:48.580+07:00  WARN 4931 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-16T14:01:49.110+07:00  INFO 4931 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-16T14:01:49.110+07:00  INFO 4931 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@58a9f110] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-16T14:01:49.188+07:00  WARN 4931 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-16T14:01:49.188+07:00  WARN 4931 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-16T14:01:49.503+07:00  INFO 4931 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-16T14:01:49.534+07:00  INFO 4931 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-16T14:01:49.539+07:00  INFO 4931 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-16T14:01:49.539+07:00  INFO 4931 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-16T14:01:49.546+07:00  WARN 4931 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-16T14:01:49.674+07:00  INFO 4931 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-16T14:01:50.196+07:00  INFO 4931 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-16T14:01:50.199+07:00  INFO 4931 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-16T14:01:50.233+07:00  INFO 4931 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-16T14:01:50.277+07:00  INFO 4931 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-16T14:01:50.359+07:00  INFO 4931 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-16T14:01:50.386+07:00  INFO 4931 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-16T14:01:50.406+07:00  INFO 4931 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 66389674ms : this is harmless.
2025-09-16T14:01:50.415+07:00  INFO 4931 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-16T14:01:50.418+07:00  INFO 4931 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-16T14:01:50.429+07:00  INFO 4931 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 357895630ms : this is harmless.
2025-09-16T14:01:50.430+07:00  INFO 4931 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-16T14:01:50.442+07:00  INFO 4931 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-16T14:01:50.443+07:00  INFO 4931 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-16T14:01:52.371+07:00  INFO 4931 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-16T14:01:52.371+07:00  INFO 4931 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-16T14:01:52.372+07:00  WARN 4931 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-16T14:01:52.658+07:00  INFO 4931 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 16/09/2025@14:00:00+0700 to 16/09/2025@14:15:00+0700
2025-09-16T14:01:52.658+07:00  INFO 4931 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 16/09/2025@14:00:00+0700 to 16/09/2025@14:15:00+0700
2025-09-16T14:01:52.796+07:00  WARN 4931 --- [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'timeOffLogic': Unsatisfied dependency expressed through field 'remainingLogic': Error creating bean with name 'timeOffRemainingLogic': Unsatisfied dependency expressed through field 'employeeLogic': Error creating bean with name 'employeeReadLogic': Unsatisfied dependency expressed through field 'accountLogic': Error creating bean with name 'accountLogic': Unsatisfied dependency expressed through field 'plugins': Error creating bean with name 'communicationAccountServicePlugin': Unsatisfied dependency expressed through field 'service': Error creating bean with name 'CommunicationMessageService': Unsatisfied dependency expressed through field 'botService': Error creating bean with name 'BotService': Unsatisfied dependency expressed through method 'register' parameter 0: Error creating bean with name 'TMSBillBotJobTrackingRuleEventHandler': Unsatisfied dependency expressed through field 'customerLogic': Error creating bean with name 'TMSCustomerLogic': Unsatisfied dependency expressed through field 'partnerLogic': Error creating bean with name 'TMSPartnerLogic': Unsatisfied dependency expressed through field 'tmsBillLogic': Error creating bean with name 'TMSBillLogic': Unsatisfied dependency expressed through field 'vendorBillLogic': Error creating bean with name 'TMSVendorBillLogic': Unsatisfied dependency expressed through field 'trackingLogic': Error creating bean with name 'vehicleTripGoodsTrackingLogic': Unsatisfied dependency expressed through field 'vehicleLogic': Error creating bean with name 'vehicleLogic': Unsatisfied dependency expressed through field 'apiAuthorizationService': Error creating bean with name 'ApiAuthorizationService': Unsatisfied dependency expressed through method 'setPlugins' parameter 0: Error creating bean with name 'syncBFSOnePartnerCodeApiPlugin': Unsatisfied dependency expressed through field 'bfsOnePartnerService': Error creating bean with name 'CRMPartnerService': Unsatisfied dependency expressed through field 'bfsonePartnerLogic': Error creating bean with name 'CRMPartnerLogic': Unsatisfied dependency expressed through field 'crmMessageLogic': Error creating bean with name 'CRMMessageLogic': Unsatisfied dependency expressed through field 'plugins': Error creating bean with name 'saleDailyTaskMessagePlugin': Unsatisfied dependency expressed through field 'saleTaskReportLogic': Error creating bean with name 'saleTaskLogic': Unsatisfied dependency expressed through field 'customerLeadsLogic': Error creating bean with name 'customerLeadsLogic': Unsatisfied dependency expressed through field 'bfsOneCRMLogic': Error creating bean with name 'BFSOneCRMLogic': Unsatisfied dependency expressed through field 'partnerRequestLogic': Error creating bean with name 'partnerRequestLogic': Unsatisfied dependency expressed through field 'requestRepo': No qualifying bean of type 'cloud.datatp.fforwarder.core.partner.request.repository.PartnerRequestRepository' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@org.springframework.beans.factory.annotation.Autowired(required=true)}
2025-09-16T14:01:52.818+07:00  INFO 4931 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-16T14:01:52.823+07:00  INFO 4931 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-16T14:01:52.834+07:00  INFO 4931 --- [main] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-16T14:01:52.834+07:00  INFO 4931 --- [main] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-16T14:01:52.834+07:00  INFO 4931 --- [main] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-16T14:01:52.834+07:00  INFO 4931 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-16T14:01:52.836+07:00  INFO 4931 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-16T14:01:52.836+07:00  INFO 4931 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-16T14:01:52.836+07:00  INFO 4931 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-16T14:01:52.836+07:00  INFO 4931 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-16T14:01:52.836+07:00  INFO 4931 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-16T14:01:52.836+07:00  INFO 4931 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-16T14:01:52.837+07:00  INFO 4931 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-16T14:01:52.837+07:00  INFO 4931 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-16T14:01:52.837+07:00  INFO 4931 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-16T14:01:52.839+07:00  INFO 4931 --- [main] org.eclipse.jetty.server.Server          : Stopped oejs.Server@676c3745{STOPPING}[12.0.15,sto=0]
2025-09-16T14:01:52.841+07:00  INFO 4931 --- [main] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@45144fdf{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.2580182593180378993/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@23c07745{STOPPED}}
2025-09-16T14:01:52.845+07:00  INFO 4931 --- [main] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-09-16T14:02:37.903+07:00 ERROR 4931 --- [main] o.s.b.d.LoggingFailureAnalysisReporter   : 

***************************
APPLICATION FAILED TO START
***************************

Description:

Field requestRepo in cloud.datatp.fforwarder.core.partner.request.PartnerRequestLogic required a bean of type 'cloud.datatp.fforwarder.core.partner.request.repository.PartnerRequestRepository' that could not be found.

The injection point has the following annotations:
	- @org.springframework.beans.factory.annotation.Autowired(required=true)


Action:

Consider defining a bean of type 'cloud.datatp.fforwarder.core.partner.request.repository.PartnerRequestRepository' in your configuration.

