2025-09-17T09:29:45.849+07:00  INFO 10751 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 10751 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-17T09:29:45.850+07:00  INFO 10751 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-17T09:29:46.568+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:29:46.634+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 62 ms. Found 22 JPA repository interfaces.
2025-09-17T09:29:46.644+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:29:46.645+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-17T09:29:46.646+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:29:46.691+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 45 ms. Found 10 JPA repository interfaces.
2025-09-17T09:29:46.692+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:29:46.695+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 3 JPA repository interfaces.
2025-09-17T09:29:46.705+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:29:46.711+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 1 JPA repository interface.
2025-09-17T09:29:46.720+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:29:46.722+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 2 JPA repository interfaces.
2025-09-17T09:29:46.722+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:29:46.726+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-17T09:29:46.729+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:29:46.735+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 5 JPA repository interfaces.
2025-09-17T09:29:46.739+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:29:46.741+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-17T09:29:46.741+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:29:46.742+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-17T09:29:46.742+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:29:46.748+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 10 JPA repository interfaces.
2025-09-17T09:29:46.753+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:29:46.755+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-17T09:29:46.758+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:29:46.762+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-17T09:29:46.762+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:29:46.770+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-09-17T09:29:46.770+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:29:46.773+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-17T09:29:46.773+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:29:46.774+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-17T09:29:46.774+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:29:46.775+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-17T09:29:46.775+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:29:46.779+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-17T09:29:46.779+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:29:46.780+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-17T09:29:46.781+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:29:46.781+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-17T09:29:46.781+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:29:46.791+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 19 JPA repository interfaces.
2025-09-17T09:29:46.801+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:29:46.806+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-09-17T09:29:46.807+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:29:46.810+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-09-17T09:29:46.810+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:29:46.813+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-17T09:29:46.814+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:29:46.819+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-09-17T09:29:46.819+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:29:46.823+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-17T09:29:46.823+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:29:46.832+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 13 JPA repository interfaces.
2025-09-17T09:29:46.832+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:29:46.842+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 14 JPA repository interfaces.
2025-09-17T09:29:46.842+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:29:46.856+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 13 ms. Found 24 JPA repository interfaces.
2025-09-17T09:29:46.856+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:29:46.857+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-17T09:29:46.863+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:29:46.864+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-17T09:29:46.864+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:29:46.871+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-09-17T09:29:46.873+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:29:46.911+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 37 ms. Found 66 JPA repository interfaces.
2025-09-17T09:29:46.911+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:29:46.912+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-17T09:29:46.917+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:29:46.920+07:00  INFO 10751 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-17T09:29:47.166+07:00  INFO 10751 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-17T09:29:47.170+07:00  INFO 10751 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-17T09:29:47.445+07:00  WARN 10751 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-17T09:29:47.657+07:00  INFO 10751 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-17T09:29:47.659+07:00  INFO 10751 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-17T09:29:47.671+07:00  INFO 10751 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-17T09:29:47.671+07:00  INFO 10751 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1696 ms
2025-09-17T09:29:47.742+07:00  WARN 10751 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-17T09:29:47.742+07:00  INFO 10751 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-17T09:29:47.858+07:00  INFO 10751 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@579b1d86
2025-09-17T09:29:47.860+07:00  INFO 10751 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-17T09:29:47.865+07:00  WARN 10751 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-17T09:29:47.865+07:00  INFO 10751 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-17T09:29:47.871+07:00  INFO 10751 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@4015c65b
2025-09-17T09:29:47.871+07:00  INFO 10751 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-17T09:29:47.871+07:00  WARN 10751 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-17T09:29:47.871+07:00  INFO 10751 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-17T09:29:47.882+07:00  INFO 10751 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@2ea3f905
2025-09-17T09:29:47.882+07:00  INFO 10751 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-17T09:29:47.882+07:00  WARN 10751 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-17T09:29:47.882+07:00  INFO 10751 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-17T09:29:47.960+07:00  INFO 10751 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@4ab8c3c0
2025-09-17T09:29:47.960+07:00  INFO 10751 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-17T09:29:47.960+07:00  WARN 10751 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-17T09:29:47.960+07:00  INFO 10751 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-17T09:29:47.968+07:00  INFO 10751 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@71a18feb
2025-09-17T09:29:47.969+07:00  INFO 10751 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-17T09:29:47.969+07:00  INFO 10751 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-17T09:29:48.015+07:00  INFO 10751 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-17T09:29:48.074+07:00  INFO 10751 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@37eca4c0{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.4480754799351732840/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@5c452eb5{STARTED}}
2025-09-17T09:29:48.074+07:00  INFO 10751 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@37eca4c0{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.4480754799351732840/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@5c452eb5{STARTED}}
2025-09-17T09:29:48.076+07:00  INFO 10751 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@7e7f72{STARTING}[12.0.15,sto=0] @2855ms
2025-09-17T09:29:48.131+07:00  INFO 10751 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-17T09:29:48.167+07:00  INFO 10751 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-17T09:29:48.184+07:00  INFO 10751 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-17T09:29:48.308+07:00  INFO 10751 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-17T09:29:48.379+07:00  WARN 10751 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-17T09:29:49.032+07:00  INFO 10751 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-17T09:29:49.039+07:00  INFO 10751 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@cdeb5f5] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-17T09:29:49.196+07:00  INFO 10751 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-17T09:29:49.485+07:00  INFO 10751 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "cloud.datatp.tms", "net.datatp.module.groovy", "net.datatp.module.company.tmpl", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.core.print", "net.datatp.module.chatbot", "cloud.datatp.module.odoo", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-09-17T09:29:49.487+07:00  INFO 10751 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-17T09:29:49.494+07:00  INFO 10751 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-17T09:29:49.496+07:00  INFO 10751 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-17T09:29:49.522+07:00  INFO 10751 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-17T09:29:49.536+07:00  WARN 10751 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-17T09:29:51.994+07:00  INFO 10751 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-17T09:29:51.995+07:00  INFO 10751 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@42f2654e] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-17T09:29:52.293+07:00  WARN 10751 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-17T09:29:52.293+07:00  WARN 10751 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-17T09:29:52.311+07:00  WARN 10751 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-17T09:29:52.311+07:00  WARN 10751 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-17T09:29:52.329+07:00  WARN 10751 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-17T09:29:52.329+07:00  WARN 10751 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-17T09:29:52.907+07:00  INFO 10751 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-17T09:29:52.914+07:00  INFO 10751 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-17T09:29:52.915+07:00  INFO 10751 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-17T09:29:52.938+07:00  INFO 10751 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-17T09:29:52.948+07:00  WARN 10751 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-17T09:29:53.615+07:00  INFO 10751 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-17T09:29:53.615+07:00  INFO 10751 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@2839b846] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-17T09:29:53.724+07:00  WARN 10751 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-17T09:29:53.724+07:00  WARN 10751 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-17T09:29:54.095+07:00  INFO 10751 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-17T09:29:54.126+07:00  INFO 10751 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-17T09:29:54.131+07:00  INFO 10751 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-17T09:29:54.131+07:00  INFO 10751 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T09:29:54.139+07:00  WARN 10751 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-17T09:29:54.271+07:00  INFO 10751 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-17T09:29:54.731+07:00  INFO 10751 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-17T09:29:54.734+07:00  INFO 10751 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-17T09:29:54.770+07:00  INFO 10751 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-17T09:29:54.819+07:00  INFO 10751 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-17T09:29:54.890+07:00  INFO 10751 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-17T09:29:54.919+07:00  INFO 10751 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-17T09:29:54.939+07:00  INFO 10751 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 85986492ms : this is harmless.
2025-09-17T09:29:54.948+07:00  INFO 10751 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-17T09:29:54.951+07:00  INFO 10751 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-17T09:29:54.962+07:00  INFO 10751 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 377492446ms : this is harmless.
2025-09-17T09:29:54.964+07:00  INFO 10751 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-17T09:29:55.008+07:00  INFO 10751 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-17T09:29:55.010+07:00  INFO 10751 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-17T09:29:57.084+07:00  INFO 10751 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-17T09:29:57.084+07:00  INFO 10751 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T09:29:57.085+07:00  WARN 10751 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-17T09:29:57.463+07:00  INFO 10751 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 17/09/2025@09:15:00+0700 to 17/09/2025@09:30:00+0700
2025-09-17T09:29:57.464+07:00  INFO 10751 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 17/09/2025@09:15:00+0700 to 17/09/2025@09:30:00+0700
2025-09-17T09:29:58.127+07:00  INFO 10751 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-17T09:29:58.127+07:00  INFO 10751 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T09:29:58.128+07:00  WARN 10751 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-17T09:29:58.436+07:00  INFO 10751 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-17T09:29:58.436+07:00  INFO 10751 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-17T09:29:58.436+07:00  INFO 10751 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-17T09:29:58.436+07:00  INFO 10751 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-17T09:29:58.436+07:00  INFO 10751 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-17T09:30:00.268+07:00  WARN 10751 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 8ad31936-f85b-4679-ac1b-187c168a2dcc

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-17T09:30:00.271+07:00  INFO 10751 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-17T09:30:00.612+07:00  INFO 10751 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-17T09:30:00.612+07:00  INFO 10751 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-17T09:30:00.613+07:00  INFO 10751 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-17T09:30:00.613+07:00  INFO 10751 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-17T09:30:00.613+07:00  INFO 10751 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-17T09:30:00.613+07:00  INFO 10751 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-17T09:30:00.613+07:00  INFO 10751 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-17T09:30:00.613+07:00  INFO 10751 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-17T09:30:00.613+07:00  INFO 10751 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-17T09:30:00.613+07:00  INFO 10751 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-17T09:30:00.613+07:00  INFO 10751 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-17T09:30:00.613+07:00  INFO 10751 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-17T09:30:00.614+07:00  INFO 10751 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-17T09:30:00.614+07:00  INFO 10751 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-17T09:30:00.614+07:00  INFO 10751 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-17T09:30:00.680+07:00  INFO 10751 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-17T09:30:00.680+07:00  INFO 10751 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-17T09:30:00.681+07:00  INFO 10751 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-09-17T09:30:00.689+07:00  INFO 10751 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@9f601f1{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-17T09:30:00.690+07:00  INFO 10751 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-17T09:30:00.691+07:00  INFO 10751 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-17T09:30:00.724+07:00  INFO 10751 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-17T09:30:00.724+07:00  INFO 10751 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-17T09:30:00.730+07:00  INFO 10751 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 15.173 seconds (process running for 15.509)
2025-09-17T09:31:03.744+07:00  INFO 10751 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T09:31:03.755+07:00  INFO 10751 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T09:31:03.761+07:00  INFO 10751 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T09:32:06.851+07:00  INFO 10751 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T09:32:29.799+07:00  INFO 10751 --- [qtp828508529-36] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0ebtd89nar1wm1jdfmi8hrf8vr0
2025-09-17T09:32:30.360+07:00  INFO 10751 --- [qtp828508529-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0ebtd89nar1wm1jdfmi8hrf8vr0, token = 800cfbd90267ff67a8d7751976e4fdff
2025-09-17T09:32:30.910+07:00  INFO 10751 --- [qtp828508529-37] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node010za2plavzpxf4tf5h62ba0sq1
2025-09-17T09:32:30.936+07:00  INFO 10751 --- [qtp828508529-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T09:32:31.078+07:00  INFO 10751 --- [qtp828508529-37] n.d.module.session.ClientSessionManager  : Add a client session id = node010za2plavzpxf4tf5h62ba0sq1, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T09:32:31.085+07:00  INFO 10751 --- [qtp828508529-37] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T09:33:02.940+07:00  INFO 10751 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T09:33:02.954+07:00  INFO 10751 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T09:33:02.960+07:00  INFO 10751 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T09:34:06.049+07:00  INFO 10751 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T09:35:02.156+07:00  INFO 10751 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T09:35:02.160+07:00  INFO 10751 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T09:35:07.211+07:00  INFO 10751 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-17T09:35:07.217+07:00  INFO 10751 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T09:35:47.196+07:00  INFO 10751 --- [qtp828508529-41] n.d.module.session.ClientSessionManager  : Add a client session id = node010za2plavzpxf4tf5h62ba0sq1, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T09:35:47.206+07:00  INFO 10751 --- [qtp828508529-41] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T09:35:47.210+07:00  INFO 10751 --- [qtp828508529-62] n.d.module.session.ClientSessionManager  : Add a client session id = node010za2plavzpxf4tf5h62ba0sq1, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T09:35:47.217+07:00  INFO 10751 --- [qtp828508529-62] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T09:36:05.330+07:00  INFO 10751 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T09:36:40.249+07:00  INFO 10751 --- [qtp828508529-37] n.d.module.session.ClientSessionManager  : Add a client session id = node010za2plavzpxf4tf5h62ba0sq1, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T09:36:40.264+07:00  INFO 10751 --- [qtp828508529-37] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T09:36:40.280+07:00  INFO 10751 --- [qtp828508529-60] n.d.module.session.ClientSessionManager  : Add a client session id = node010za2plavzpxf4tf5h62ba0sq1, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T09:36:40.289+07:00  INFO 10751 --- [qtp828508529-60] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T09:37:06.452+07:00  INFO 10751 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T09:37:07.527+07:00  INFO 10751 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-17T09:37:07.552+07:00  INFO 10751 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T09:38:04.653+07:00  INFO 10751 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T09:38:43.202+07:00  INFO 10751 --- [qtp828508529-41] n.d.module.session.ClientSessionManager  : Add a client session id = node010za2plavzpxf4tf5h62ba0sq1, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T09:38:43.203+07:00  INFO 10751 --- [qtp828508529-62] n.d.module.session.ClientSessionManager  : Add a client session id = node010za2plavzpxf4tf5h62ba0sq1, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T09:38:43.220+07:00  INFO 10751 --- [qtp828508529-62] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T09:38:43.220+07:00  INFO 10751 --- [qtp828508529-41] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T09:38:55.187+07:00  INFO 10751 --- [qtp828508529-65] n.d.module.session.ClientSessionManager  : Add a client session id = node010za2plavzpxf4tf5h62ba0sq1, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T09:38:55.190+07:00  INFO 10751 --- [qtp828508529-61] n.d.module.session.ClientSessionManager  : Add a client session id = node010za2plavzpxf4tf5h62ba0sq1, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T09:38:55.213+07:00  INFO 10751 --- [qtp828508529-65] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T09:38:55.213+07:00  INFO 10751 --- [qtp828508529-61] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T09:39:03.020+07:00  INFO 10751 --- [qtp828508529-37] n.d.module.session.ClientSessionManager  : Add a client session id = node010za2plavzpxf4tf5h62ba0sq1, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T09:39:03.039+07:00  INFO 10751 --- [qtp828508529-37] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T09:39:03.050+07:00  INFO 10751 --- [qtp828508529-40] n.d.module.session.ClientSessionManager  : Add a client session id = node010za2plavzpxf4tf5h62ba0sq1, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T09:39:03.069+07:00  INFO 10751 --- [qtp828508529-40] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T09:39:06.758+07:00  INFO 10751 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T09:39:06.765+07:00  INFO 10751 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T09:39:06.768+07:00  INFO 10751 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T09:39:14.289+07:00  INFO 10751 --- [qtp828508529-41] n.d.module.session.ClientSessionManager  : Add a client session id = node010za2plavzpxf4tf5h62ba0sq1, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T09:39:14.300+07:00  INFO 10751 --- [qtp828508529-41] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T09:39:14.316+07:00  INFO 10751 --- [qtp828508529-68] n.d.module.session.ClientSessionManager  : Add a client session id = node010za2plavzpxf4tf5h62ba0sq1, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T09:39:14.324+07:00  INFO 10751 --- [qtp828508529-68] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T09:39:25.327+07:00  INFO 10751 --- [qtp828508529-40] n.d.module.session.ClientSessionManager  : Add a client session id = node010za2plavzpxf4tf5h62ba0sq1, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T09:39:25.331+07:00  INFO 10751 --- [qtp828508529-61] n.d.module.session.ClientSessionManager  : Add a client session id = node010za2plavzpxf4tf5h62ba0sq1, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T09:39:25.340+07:00  INFO 10751 --- [qtp828508529-61] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T09:39:25.340+07:00  INFO 10751 --- [qtp828508529-40] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T09:39:43.213+07:00  INFO 10751 --- [qtp828508529-61] n.d.module.session.ClientSessionManager  : Add a client session id = node010za2plavzpxf4tf5h62ba0sq1, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T09:39:43.216+07:00  INFO 10751 --- [qtp828508529-65] n.d.module.session.ClientSessionManager  : Add a client session id = node010za2plavzpxf4tf5h62ba0sq1, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T09:39:43.246+07:00  INFO 10751 --- [qtp828508529-65] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T09:39:43.246+07:00  INFO 10751 --- [qtp828508529-61] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T09:39:48.020+07:00  INFO 10751 --- [Scheduler-*********-1] n.d.m.session.AppHttpSessionListener     : The session node0ebtd89nar1wm1jdfmi8hrf8vr0 is destroyed.
2025-09-17T09:40:03.882+07:00  INFO 10751 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T09:40:03.884+07:00  INFO 10751 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T09:40:34.231+07:00  INFO 10751 --- [qtp828508529-68] n.d.module.session.ClientSessionManager  : Add a client session id = node010za2plavzpxf4tf5h62ba0sq1, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T09:40:34.251+07:00  INFO 10751 --- [qtp828508529-68] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T09:40:34.270+07:00  INFO 10751 --- [qtp828508529-64] n.d.module.session.ClientSessionManager  : Add a client session id = node010za2plavzpxf4tf5h62ba0sq1, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T09:40:34.293+07:00  INFO 10751 --- [qtp828508529-64] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T09:41:06.997+07:00  INFO 10751 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T09:41:07.055+07:00  INFO 10751 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-09-17T09:41:07.064+07:00  INFO 10751 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T09:41:13.324+07:00  INFO 10751 --- [qtp828508529-61] n.d.module.session.ClientSessionManager  : Add a client session id = node010za2plavzpxf4tf5h62ba0sq1, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T09:41:13.325+07:00  INFO 10751 --- [qtp828508529-40] n.d.module.session.ClientSessionManager  : Add a client session id = node010za2plavzpxf4tf5h62ba0sq1, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T09:41:13.336+07:00  INFO 10751 --- [qtp828508529-61] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T09:41:13.336+07:00  INFO 10751 --- [qtp828508529-40] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T09:41:37.486+07:00  INFO 10751 --- [qtp828508529-62] n.d.module.session.ClientSessionManager  : Add a client session id = node010za2plavzpxf4tf5h62ba0sq1, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T09:41:37.488+07:00  INFO 10751 --- [qtp828508529-41] n.d.module.session.ClientSessionManager  : Add a client session id = node010za2plavzpxf4tf5h62ba0sq1, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T09:41:37.497+07:00  INFO 10751 --- [qtp828508529-62] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T09:41:37.498+07:00  INFO 10751 --- [qtp828508529-41] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T09:41:40.439+07:00  INFO 10751 --- [qtp828508529-40] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T09:41:40.441+07:00  INFO 10751 --- [qtp828508529-61] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T09:41:40.585+07:00  INFO 10751 --- [qtp828508529-61] c.d.f.s.report.PerformanceReportLogic    : Retrieved 17 records
2025-09-17T09:41:40.585+07:00  INFO 10751 --- [qtp828508529-40] c.d.f.s.report.PerformanceReportLogic    : Retrieved 17 records
2025-09-17T09:41:40.597+07:00  INFO 10751 --- [qtp828508529-40] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T09:41:40.615+07:00  INFO 10751 --- [qtp828508529-88] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T09:41:41.199+07:00  INFO 10751 --- [qtp828508529-88] c.d.f.s.report.PerformanceReportLogic    : Retrieved 21 records
2025-09-17T09:41:41.202+07:00  INFO 10751 --- [qtp828508529-40] c.d.f.s.report.PerformanceReportLogic    : Retrieved 21 records
2025-09-17T09:42:03.145+07:00  INFO 10751 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T09:43:06.242+07:00  INFO 10751 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T09:43:06.283+07:00  INFO 10751 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 1
2025-09-17T09:43:06.287+07:00  INFO 10751 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T09:44:02.368+07:00  INFO 10751 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T09:45:05.476+07:00  INFO 10751 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-17T09:45:05.486+07:00  INFO 10751 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 17/09/2025@09:45:05+0700
2025-09-17T09:45:05.553+07:00  INFO 10751 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 17/09/2025@09:45:00+0700 to 17/09/2025@10:00:00+0700
2025-09-17T09:45:05.554+07:00  INFO 10751 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 17/09/2025@09:45:00+0700 to 17/09/2025@10:00:00+0700
2025-09-17T09:45:05.554+07:00  INFO 10751 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T09:45:05.554+07:00  INFO 10751 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T09:45:05.579+07:00  INFO 10751 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 0
2025-09-17T09:45:05.584+07:00  INFO 10751 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T09:46:06.712+07:00  INFO 10751 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T09:47:04.812+07:00  INFO 10751 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T09:47:04.825+07:00  INFO 10751 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T09:47:04.835+07:00  INFO 10751 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T09:48:06.926+07:00  INFO 10751 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T09:49:04.026+07:00  INFO 10751 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T09:49:04.073+07:00  INFO 10751 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-09-17T09:49:04.083+07:00  INFO 10751 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T09:49:35.947+07:00  INFO 10751 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@9f601f1{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-17T09:49:35.948+07:00  INFO 10751 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-17T09:49:35.948+07:00  INFO 10751 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-17T09:49:35.948+07:00  INFO 10751 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-17T09:49:35.948+07:00  INFO 10751 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-17T09:49:35.949+07:00  INFO 10751 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-17T09:49:35.949+07:00  INFO 10751 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-17T09:49:35.949+07:00  INFO 10751 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-17T09:49:35.949+07:00  INFO 10751 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-17T09:49:35.949+07:00  INFO 10751 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-17T09:49:35.949+07:00  INFO 10751 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-17T09:49:35.949+07:00  INFO 10751 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-17T09:49:35.949+07:00  INFO 10751 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-17T09:49:35.949+07:00  INFO 10751 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-17T09:49:35.949+07:00  INFO 10751 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-17T09:49:35.949+07:00  INFO 10751 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-17T09:49:35.964+07:00  INFO 10751 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T09:49:36.012+07:00  INFO 10751 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-17T09:49:36.017+07:00  INFO 10751 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-17T09:49:36.039+07:00  INFO 10751 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-17T09:49:36.039+07:00  INFO 10751 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-17T09:49:36.040+07:00  INFO 10751 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-17T09:49:36.040+07:00  INFO 10751 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-17T09:49:36.041+07:00  INFO 10751 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-17T09:49:36.041+07:00  INFO 10751 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-17T09:49:36.041+07:00  INFO 10751 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-17T09:49:36.041+07:00  INFO 10751 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-17T09:49:36.041+07:00  INFO 10751 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-17T09:49:36.042+07:00  INFO 10751 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-17T09:49:36.042+07:00  INFO 10751 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-17T09:49:36.042+07:00  INFO 10751 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-17T09:49:36.042+07:00  INFO 10751 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-17T09:49:36.044+07:00  INFO 10751 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@7e7f72{STOPPING}[12.0.15,sto=0]
2025-09-17T09:49:36.046+07:00  INFO 10751 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-17T09:49:36.047+07:00  INFO 10751 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@37eca4c0{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.4480754799351732840/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@5c452eb5{STOPPED}}
