2025-09-17T09:59:18.708+07:00  INFO 14574 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 14574 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-17T09:59:18.709+07:00  INFO 14574 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-17T09:59:19.392+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:59:19.455+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 60 ms. Found 22 JPA repository interfaces.
2025-09-17T09:59:19.465+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:59:19.466+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-17T09:59:19.467+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:59:19.512+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 45 ms. Found 10 JPA repository interfaces.
2025-09-17T09:59:19.513+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:59:19.516+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-17T09:59:19.524+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:59:19.529+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-09-17T09:59:19.537+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:59:19.540+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 2 JPA repository interfaces.
2025-09-17T09:59:19.540+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:59:19.544+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-17T09:59:19.546+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:59:19.550+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-09-17T09:59:19.554+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:59:19.557+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-17T09:59:19.557+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:59:19.557+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-17T09:59:19.557+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:59:19.564+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 10 JPA repository interfaces.
2025-09-17T09:59:19.569+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:59:19.572+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-17T09:59:19.575+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:59:19.580+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-09-17T09:59:19.580+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:59:19.588+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 12 JPA repository interfaces.
2025-09-17T09:59:19.589+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:59:19.592+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-09-17T09:59:19.593+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:59:19.593+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-17T09:59:19.593+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:59:19.594+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-17T09:59:19.594+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:59:19.600+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 7 JPA repository interfaces.
2025-09-17T09:59:19.600+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:59:19.601+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-17T09:59:19.602+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:59:19.602+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-17T09:59:19.602+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:59:19.613+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 19 JPA repository interfaces.
2025-09-17T09:59:19.623+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:59:19.630+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 8 JPA repository interfaces.
2025-09-17T09:59:19.631+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:59:19.634+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-09-17T09:59:19.634+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:59:19.638+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-09-17T09:59:19.639+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:59:19.644+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-09-17T09:59:19.644+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:59:19.648+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-17T09:59:19.648+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:59:19.657+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 13 JPA repository interfaces.
2025-09-17T09:59:19.657+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:59:19.666+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 14 JPA repository interfaces.
2025-09-17T09:59:19.666+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:59:19.693+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 26 ms. Found 24 JPA repository interfaces.
2025-09-17T09:59:19.694+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:59:19.696+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-09-17T09:59:19.703+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:59:19.704+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-17T09:59:19.705+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:59:19.714+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 12 JPA repository interfaces.
2025-09-17T09:59:19.718+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:59:19.763+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 45 ms. Found 66 JPA repository interfaces.
2025-09-17T09:59:19.763+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:59:19.765+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-17T09:59:19.769+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T09:59:19.772+07:00  INFO 14574 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-17T09:59:19.982+07:00  INFO 14574 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-17T09:59:19.986+07:00  INFO 14574 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-17T09:59:20.261+07:00  WARN 14574 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-17T09:59:20.455+07:00  INFO 14574 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-17T09:59:20.457+07:00  INFO 14574 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-17T09:59:20.468+07:00  INFO 14574 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-17T09:59:20.469+07:00  INFO 14574 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1650 ms
2025-09-17T09:59:20.518+07:00  WARN 14574 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-17T09:59:20.518+07:00  INFO 14574 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-17T09:59:20.615+07:00  INFO 14574 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@42a08547
2025-09-17T09:59:20.616+07:00  INFO 14574 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-17T09:59:20.620+07:00  WARN 14574 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-17T09:59:20.621+07:00  INFO 14574 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-17T09:59:20.626+07:00  INFO 14574 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@6fb8cfa7
2025-09-17T09:59:20.626+07:00  INFO 14574 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-17T09:59:20.626+07:00  WARN 14574 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-17T09:59:20.626+07:00  INFO 14574 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-17T09:59:20.632+07:00  INFO 14574 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@53af65eb
2025-09-17T09:59:20.632+07:00  INFO 14574 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-17T09:59:20.632+07:00  WARN 14574 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-17T09:59:20.632+07:00  INFO 14574 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-17T09:59:20.640+07:00  INFO 14574 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@340b7fca
2025-09-17T09:59:20.640+07:00  INFO 14574 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-17T09:59:20.640+07:00  WARN 14574 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-17T09:59:20.641+07:00  INFO 14574 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-17T09:59:20.647+07:00  INFO 14574 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@4e92c6c2
2025-09-17T09:59:20.648+07:00  INFO 14574 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-17T09:59:20.648+07:00  INFO 14574 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-17T09:59:20.693+07:00  INFO 14574 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-17T09:59:20.694+07:00  INFO 14574 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@37846cc2{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.16752132726298827578/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@55e8522e{STARTED}}
2025-09-17T09:59:20.695+07:00  INFO 14574 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@37846cc2{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.16752132726298827578/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@55e8522e{STARTED}}
2025-09-17T09:59:20.744+07:00  INFO 14574 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@3787eac3{STARTING}[12.0.15,sto=0] @2648ms
2025-09-17T09:59:20.800+07:00  INFO 14574 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-17T09:59:20.827+07:00  INFO 14574 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-17T09:59:20.842+07:00  INFO 14574 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-17T09:59:20.964+07:00  INFO 14574 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-17T09:59:21.073+07:00  WARN 14574 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-17T09:59:21.728+07:00  INFO 14574 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-17T09:59:21.735+07:00  INFO 14574 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@2e27c2b2] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-17T09:59:21.880+07:00  INFO 14574 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-17T09:59:22.086+07:00  INFO 14574 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.graphapi", "net.datatp.module.kpi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "cloud.datatp.tms", "net.datatp.module.groovy", "net.datatp.module.company.tmpl", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "cloud.datatp.module.odoo", "net.datatp.module.core.print", "net.datatp.module.chatbot", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.zalo", "net.datatp.module.accounting" ]
2025-09-17T09:59:22.088+07:00  INFO 14574 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-17T09:59:22.095+07:00  INFO 14574 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-17T09:59:22.096+07:00  INFO 14574 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-17T09:59:22.122+07:00  INFO 14574 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-17T09:59:22.132+07:00  WARN 14574 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-17T09:59:24.227+07:00  INFO 14574 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-17T09:59:24.228+07:00  INFO 14574 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@21ea37f8] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-17T09:59:24.458+07:00  WARN 14574 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-17T09:59:24.458+07:00  WARN 14574 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-17T09:59:24.474+07:00  WARN 14574 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-17T09:59:24.474+07:00  WARN 14574 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-17T09:59:24.486+07:00  WARN 14574 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-17T09:59:24.486+07:00  WARN 14574 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-17T09:59:24.991+07:00  INFO 14574 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-17T09:59:24.997+07:00  INFO 14574 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-17T09:59:24.998+07:00  INFO 14574 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-17T09:59:25.018+07:00  INFO 14574 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-17T09:59:25.021+07:00  WARN 14574 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-17T09:59:25.527+07:00  INFO 14574 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-17T09:59:25.528+07:00  INFO 14574 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@54d6e8db] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-17T09:59:25.590+07:00  WARN 14574 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-17T09:59:25.590+07:00  WARN 14574 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-17T09:59:26.005+07:00  INFO 14574 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-17T09:59:26.034+07:00  INFO 14574 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-17T09:59:26.039+07:00  INFO 14574 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-17T09:59:26.039+07:00  INFO 14574 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T09:59:26.045+07:00  WARN 14574 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-17T09:59:26.191+07:00  INFO 14574 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-17T09:59:26.652+07:00  INFO 14574 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-17T09:59:26.655+07:00  INFO 14574 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-17T09:59:26.690+07:00  INFO 14574 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-17T09:59:26.732+07:00  INFO 14574 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-17T09:59:31.765+07:00  INFO 14574 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-17T09:59:31.813+07:00  INFO 14574 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-17T09:59:31.842+07:00  INFO 14574 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 145229671ms : this is harmless.
2025-09-17T09:59:31.854+07:00  INFO 14574 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-17T09:59:31.859+07:00  WARN 14574 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Attempting to allocate 100.0MB of offheap when there is only 86.8MB of free physical memory - some paging will therefore occur.
2025-09-17T09:59:31.859+07:00  INFO 14574 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-17T09:59:31.875+07:00  INFO 14574 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 436735626ms : this is harmless.
2025-09-17T09:59:31.878+07:00  INFO 14574 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-17T09:59:31.911+07:00  INFO 14574 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-17T09:59:31.912+07:00  INFO 14574 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-17T09:59:34.094+07:00  INFO 14574 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-17T09:59:34.094+07:00  INFO 14574 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T09:59:34.095+07:00  WARN 14574 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-17T09:59:34.418+07:00  INFO 14574 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 17/09/2025@09:45:00+0700 to 17/09/2025@10:00:00+0700
2025-09-17T09:59:34.419+07:00  INFO 14574 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 17/09/2025@09:45:00+0700 to 17/09/2025@10:00:00+0700
2025-09-17T09:59:34.994+07:00  INFO 14574 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-17T09:59:34.994+07:00  INFO 14574 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T09:59:34.994+07:00  WARN 14574 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-17T09:59:35.313+07:00  INFO 14574 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-17T09:59:35.313+07:00  INFO 14574 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-17T09:59:35.313+07:00  INFO 14574 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-17T09:59:35.313+07:00  INFO 14574 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-17T09:59:35.313+07:00  INFO 14574 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-17T09:59:37.566+07:00  WARN 14574 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 9676a58b-91fd-43f7-b2e6-687869f5c3e8

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-17T09:59:37.570+07:00  INFO 14574 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-17T09:59:37.931+07:00  INFO 14574 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-17T09:59:37.931+07:00  INFO 14574 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-17T09:59:37.932+07:00  INFO 14574 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-17T09:59:37.932+07:00  INFO 14574 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-17T09:59:37.932+07:00  INFO 14574 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-17T09:59:37.932+07:00  INFO 14574 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-17T09:59:37.932+07:00  INFO 14574 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-17T09:59:37.932+07:00  INFO 14574 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-17T09:59:37.932+07:00  INFO 14574 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-17T09:59:37.932+07:00  INFO 14574 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-17T09:59:37.932+07:00  INFO 14574 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-17T09:59:37.932+07:00  INFO 14574 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-17T09:59:37.935+07:00  INFO 14574 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-17T09:59:37.935+07:00  INFO 14574 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-17T09:59:37.935+07:00  INFO 14574 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-17T09:59:42.955+07:00  INFO 14574 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-17T09:59:42.955+07:00  INFO 14574 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-17T09:59:42.962+07:00  INFO 14574 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 6 ms
2025-09-17T09:59:42.980+07:00  INFO 14574 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@75a0e939{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-17T09:59:42.982+07:00  INFO 14574 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-17T09:59:42.983+07:00  INFO 14574 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-17T09:59:43.050+07:00  INFO 14574 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-17T09:59:43.051+07:00  INFO 14574 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-17T09:59:43.062+07:00  INFO 14574 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 24.665 seconds (process running for 24.965)
2025-09-17T09:59:43.749+07:00  INFO 14574 --- [qtp1661376917-36] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-09-17T10:00:00.712+07:00  INFO 14574 --- [qtp1661376917-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node015af6cy1ygtn91oj9eenmobdez1
2025-09-17T10:00:00.712+07:00  INFO 14574 --- [qtp1661376917-41] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01jdk4ev6z4y09gsszvroni0w80
2025-09-17T10:00:00.830+07:00  INFO 14574 --- [qtp1661376917-35] n.d.module.session.ClientSessionManager  : Add a client session id = node015af6cy1ygtn91oj9eenmobdez1, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:00:00.830+07:00  INFO 14574 --- [qtp1661376917-41] n.d.module.session.ClientSessionManager  : Add a client session id = node01jdk4ev6z4y09gsszvroni0w80, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:00:01.330+07:00  INFO 14574 --- [qtp1661376917-35] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:00:01.338+07:00  INFO 14574 --- [qtp1661376917-41] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:00:05.985+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-17T10:00:05.987+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:00:05.987+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T10:00:05.987+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-09-17T10:00:05.987+07:00  INFO 14574 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 17/09/2025@10:00:05+0700
2025-09-17T10:00:05.989+07:00  INFO 14574 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 17/09/2025@10:00:00+0700 to 17/09/2025@10:15:00+0700
2025-09-17T10:00:05.989+07:00  INFO 14574 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 17/09/2025@10:00:00+0700 to 17/09/2025@10:15:00+0700
2025-09-17T10:00:07.646+07:00  INFO 14574 --- [qtp1661376917-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T10:00:07.646+07:00  INFO 14574 --- [qtp1661376917-62] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T10:00:07.738+07:00  INFO 14574 --- [qtp1661376917-36] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-17T10:00:07.763+07:00  INFO 14574 --- [qtp1661376917-62] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-17T10:00:48.084+07:00  INFO 14574 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 0
2025-09-17T10:00:48.096+07:00  INFO 14574 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-17T10:01:02.123+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:02:05.226+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:02:43.846+07:00  INFO 14574 --- [qtp1661376917-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T10:02:43.846+07:00  INFO 14574 --- [qtp1661376917-40] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T10:02:43.866+07:00  INFO 14574 --- [qtp1661376917-36] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-17T10:02:43.868+07:00  INFO 14574 --- [qtp1661376917-40] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-17T10:02:47.303+07:00  INFO 14574 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-17T10:02:47.308+07:00  INFO 14574 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T10:03:06.334+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:04:04.434+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:04:46.502+07:00  INFO 14574 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 0
2025-09-17T10:04:46.518+07:00  INFO 14574 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T10:05:06.551+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:05:06.552+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T10:05:42.975+07:00  INFO 14574 --- [qtp1661376917-41] n.d.module.session.ClientSessionManager  : Add a client session id = node01jdk4ev6z4y09gsszvroni0w80, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:05:42.994+07:00  INFO 14574 --- [qtp1661376917-41] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:05:43.014+07:00  INFO 14574 --- [qtp1661376917-40] n.d.module.session.ClientSessionManager  : Add a client session id = node01jdk4ev6z4y09gsszvroni0w80, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:05:43.024+07:00  INFO 14574 --- [qtp1661376917-40] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:05:47.795+07:00  INFO 14574 --- [qtp1661376917-64] n.d.module.session.ClientSessionManager  : Add a client session id = node01jdk4ev6z4y09gsszvroni0w80, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:05:47.795+07:00  INFO 14574 --- [qtp1661376917-62] n.d.module.session.ClientSessionManager  : Add a client session id = node01jdk4ev6z4y09gsszvroni0w80, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:05:47.807+07:00  INFO 14574 --- [qtp1661376917-62] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:05:47.807+07:00  INFO 14574 --- [qtp1661376917-64] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:06:03.690+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:06:45.793+07:00  INFO 14574 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-09-17T10:06:45.803+07:00  INFO 14574 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T10:07:06.841+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:07:17.944+07:00  INFO 14574 --- [qtp1661376917-76] n.d.module.session.ClientSessionManager  : Add a client session id = node01jdk4ev6z4y09gsszvroni0w80, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:07:17.947+07:00  INFO 14574 --- [qtp1661376917-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01jdk4ev6z4y09gsszvroni0w80, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:07:17.956+07:00  INFO 14574 --- [qtp1661376917-39] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:07:17.956+07:00  INFO 14574 --- [qtp1661376917-76] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:07:30.097+07:00  INFO 14574 --- [qtp1661376917-76] n.d.module.session.ClientSessionManager  : Add a client session id = node01jdk4ev6z4y09gsszvroni0w80, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:07:30.110+07:00  INFO 14574 --- [qtp1661376917-76] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:07:30.132+07:00  INFO 14574 --- [qtp1661376917-73] n.d.module.session.ClientSessionManager  : Add a client session id = node01jdk4ev6z4y09gsszvroni0w80, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:07:30.144+07:00  INFO 14574 --- [qtp1661376917-73] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:07:37.078+07:00  INFO 14574 --- [qtp1661376917-74] n.d.module.session.ClientSessionManager  : Add a client session id = node01jdk4ev6z4y09gsszvroni0w80, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:07:37.079+07:00  INFO 14574 --- [qtp1661376917-79] n.d.module.session.ClientSessionManager  : Add a client session id = node01jdk4ev6z4y09gsszvroni0w80, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:07:37.094+07:00  INFO 14574 --- [qtp1661376917-79] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:07:37.094+07:00  INFO 14574 --- [qtp1661376917-74] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:07:50.116+07:00  INFO 14574 --- [qtp1661376917-74] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T10:07:50.116+07:00  INFO 14574 --- [qtp1661376917-76] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T10:07:50.130+07:00  INFO 14574 --- [qtp1661376917-76] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-17T10:07:50.130+07:00  INFO 14574 --- [qtp1661376917-74] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-17T10:08:02.939+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:08:26.265+07:00  INFO 14574 --- [qtp1661376917-74] n.d.module.session.ClientSessionManager  : Add a client session id = node01jdk4ev6z4y09gsszvroni0w80, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:08:26.268+07:00  INFO 14574 --- [qtp1661376917-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01jdk4ev6z4y09gsszvroni0w80, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:08:26.287+07:00  INFO 14574 --- [qtp1661376917-74] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:08:26.287+07:00  INFO 14574 --- [qtp1661376917-39] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:08:28.874+07:00  INFO 14574 --- [qtp1661376917-39] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T10:08:28.874+07:00  INFO 14574 --- [qtp1661376917-71] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T10:08:28.915+07:00  INFO 14574 --- [qtp1661376917-71] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-17T10:08:28.915+07:00  INFO 14574 --- [qtp1661376917-39] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-17T10:08:50.052+07:00  INFO 14574 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 15, expire count 0
2025-09-17T10:08:50.066+07:00  INFO 14574 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-17T10:09:06.092+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:09:15.607+07:00  INFO 14574 --- [qtp1661376917-62] n.d.module.session.ClientSessionManager  : Add a client session id = node01jdk4ev6z4y09gsszvroni0w80, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:09:15.608+07:00  INFO 14574 --- [qtp1661376917-76] n.d.module.session.ClientSessionManager  : Add a client session id = node01jdk4ev6z4y09gsszvroni0w80, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:09:15.614+07:00  INFO 14574 --- [qtp1661376917-62] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:09:15.621+07:00  INFO 14574 --- [qtp1661376917-76] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:09:20.734+07:00  INFO 14574 --- [Scheduler-1372526568-1] n.d.m.session.AppHttpSessionListener     : The session node015af6cy1ygtn91oj9eenmobdez1 is destroyed.
2025-09-17T10:09:21.375+07:00  INFO 14574 --- [qtp1661376917-39] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint PartnerRequestService/searchPartnerRequests
2025-09-17T10:09:21.374+07:00  INFO 14574 --- [qtp1661376917-74] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint PartnerRequestService/searchPartnerRequests
2025-09-17T10:09:22.372+07:00  INFO 14574 --- [qtp1661376917-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01jdk4ev6z4y09gsszvroni0w80, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:09:22.373+07:00  INFO 14574 --- [qtp1661376917-82] n.d.module.session.ClientSessionManager  : Add a client session id = node01jdk4ev6z4y09gsszvroni0w80, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:09:22.382+07:00  INFO 14574 --- [qtp1661376917-82] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:09:22.382+07:00  INFO 14574 --- [qtp1661376917-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:10:02.183+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:10:02.187+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T10:10:49.254+07:00  INFO 14574 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 3
2025-09-17T10:10:49.259+07:00  INFO 14574 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T10:11:05.295+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:12:06.384+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:12:49.506+07:00  INFO 14574 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 3
2025-09-17T10:12:49.520+07:00  INFO 14574 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T10:13:04.552+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:14:06.654+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:14:49.750+07:00  INFO 14574 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-17T10:14:49.763+07:00  INFO 14574 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T10:15:03.781+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:15:03.783+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T10:15:03.784+07:00  INFO 14574 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 17/09/2025@10:15:03+0700
2025-09-17T10:15:03.810+07:00  INFO 14574 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 17/09/2025@10:15:00+0700 to 17/09/2025@10:30:00+0700
2025-09-17T10:15:03.811+07:00  INFO 14574 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 17/09/2025@10:15:00+0700 to 17/09/2025@10:30:00+0700
2025-09-17T10:15:03.811+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-17T10:16:06.927+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:16:49.001+07:00  INFO 14574 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 2
2025-09-17T10:16:49.008+07:00  INFO 14574 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T10:17:01.014+07:00  INFO 14574 --- [qtp1661376917-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01jdk4ev6z4y09gsszvroni0w80, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:17:01.026+07:00  INFO 14574 --- [qtp1661376917-39] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:17:01.073+07:00  INFO 14574 --- [qtp1661376917-72] n.d.module.session.ClientSessionManager  : Add a client session id = node01jdk4ev6z4y09gsszvroni0w80, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:17:01.100+07:00  INFO 14574 --- [qtp1661376917-72] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:17:03.030+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:17:08.388+07:00  INFO 14574 --- [qtp1661376917-34] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T10:17:08.388+07:00  INFO 14574 --- [qtp1661376917-71] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T10:17:08.587+07:00  INFO 14574 --- [qtp1661376917-34] c.d.f.s.report.PerformanceReportLogic    : Retrieved 17 records
2025-09-17T10:17:08.587+07:00  INFO 14574 --- [qtp1661376917-71] c.d.f.s.report.PerformanceReportLogic    : Retrieved 17 records
2025-09-17T10:17:08.604+07:00  INFO 14574 --- [qtp1661376917-83] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T10:17:08.604+07:00  INFO 14574 --- [qtp1661376917-79] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T10:17:09.124+07:00  INFO 14574 --- [qtp1661376917-83] c.d.f.s.report.PerformanceReportLogic    : Retrieved 21 records
2025-09-17T10:17:09.128+07:00  INFO 14574 --- [qtp1661376917-79] c.d.f.s.report.PerformanceReportLogic    : Retrieved 21 records
2025-09-17T10:18:06.148+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:18:48.254+07:00  INFO 14574 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 6
2025-09-17T10:18:48.276+07:00  INFO 14574 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T10:19:02.294+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:20:05.387+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:20:05.390+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T10:20:47.465+07:00  INFO 14574 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 5
2025-09-17T10:20:47.473+07:00  INFO 14574 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T10:21:06.496+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:21:46.169+07:00  INFO 14574 --- [qtp1661376917-82] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T10:21:46.169+07:00  INFO 14574 --- [qtp1661376917-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T10:21:46.190+07:00  INFO 14574 --- [qtp1661376917-82] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-17T10:21:46.190+07:00  INFO 14574 --- [qtp1661376917-36] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-17T10:22:04.603+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:22:46.738+07:00  INFO 14574 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 13, expire count 0
2025-09-17T10:22:46.761+07:00  INFO 14574 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-17T10:23:06.797+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:23:39.511+07:00  INFO 14574 --- [qtp1661376917-62] n.d.module.session.ClientSessionManager  : Add a client session id = node01jdk4ev6z4y09gsszvroni0w80, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:23:39.523+07:00  INFO 14574 --- [qtp1661376917-62] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:23:39.535+07:00  INFO 14574 --- [qtp1661376917-60] n.d.module.session.ClientSessionManager  : Add a client session id = node01jdk4ev6z4y09gsszvroni0w80, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:23:39.540+07:00  INFO 14574 --- [qtp1661376917-60] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:24:03.895+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:24:04.975+07:00  INFO 14574 --- [qtp1661376917-82] n.d.module.session.ClientSessionManager  : Add a client session id = node01jdk4ev6z4y09gsszvroni0w80, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:24:04.981+07:00  INFO 14574 --- [qtp1661376917-82] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:24:05.001+07:00  INFO 14574 --- [qtp1661376917-71] n.d.module.session.ClientSessionManager  : Add a client session id = node01jdk4ev6z4y09gsszvroni0w80, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:24:05.009+07:00  INFO 14574 --- [qtp1661376917-71] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:24:44.721+07:00  INFO 14574 --- [qtp1661376917-130] n.d.module.session.ClientSessionManager  : Add a client session id = node01jdk4ev6z4y09gsszvroni0w80, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:24:44.726+07:00  INFO 14574 --- [qtp1661376917-34] n.d.module.session.ClientSessionManager  : Add a client session id = node01jdk4ev6z4y09gsszvroni0w80, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:24:44.747+07:00  INFO 14574 --- [qtp1661376917-130] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:24:44.747+07:00  INFO 14574 --- [qtp1661376917-34] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:24:45.981+07:00  INFO 14574 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-17T10:24:45.989+07:00  INFO 14574 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T10:25:06.025+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:25:06.026+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T10:26:03.125+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:26:45.237+07:00  INFO 14574 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 1
2025-09-17T10:26:45.248+07:00  INFO 14574 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T10:27:06.285+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:27:29.929+07:00  INFO 14574 --- [qtp1661376917-71] n.d.module.session.ClientSessionManager  : Add a client session id = node01jdk4ev6z4y09gsszvroni0w80, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:27:29.930+07:00  INFO 14574 --- [qtp1661376917-82] n.d.module.session.ClientSessionManager  : Add a client session id = node01jdk4ev6z4y09gsszvroni0w80, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:27:29.943+07:00  INFO 14574 --- [qtp1661376917-82] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:27:29.943+07:00  INFO 14574 --- [qtp1661376917-71] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:28:02.388+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:28:48.304+07:00  INFO 14574 --- [qtp1661376917-34] n.d.module.session.ClientSessionManager  : Add a client session id = node01jdk4ev6z4y09gsszvroni0w80, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:28:48.305+07:00  INFO 14574 --- [qtp1661376917-71] n.d.module.session.ClientSessionManager  : Add a client session id = node01jdk4ev6z4y09gsszvroni0w80, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:28:48.310+07:00  INFO 14574 --- [qtp1661376917-71] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:28:48.310+07:00  INFO 14574 --- [qtp1661376917-34] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:28:49.489+07:00  INFO 14574 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 1
2025-09-17T10:28:49.502+07:00  INFO 14574 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T10:28:57.652+07:00  INFO 14574 --- [qtp1661376917-82] n.d.module.session.ClientSessionManager  : Add a client session id = node01jdk4ev6z4y09gsszvroni0w80, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:28:57.653+07:00  INFO 14574 --- [qtp1661376917-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01jdk4ev6z4y09gsszvroni0w80, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:28:57.663+07:00  INFO 14574 --- [qtp1661376917-82] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:28:57.676+07:00  INFO 14574 --- [qtp1661376917-39] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:29:00.099+07:00  INFO 14574 --- [qtp1661376917-34] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T10:29:00.108+07:00  INFO 14574 --- [qtp1661376917-79] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T10:29:00.149+07:00  INFO 14574 --- [qtp1661376917-79] c.d.f.s.report.PerformanceReportLogic    : Retrieved 17 records
2025-09-17T10:29:00.149+07:00  INFO 14574 --- [qtp1661376917-34] c.d.f.s.report.PerformanceReportLogic    : Retrieved 17 records
2025-09-17T10:29:00.166+07:00  INFO 14574 --- [qtp1661376917-34] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T10:29:00.167+07:00  INFO 14574 --- [qtp1661376917-79] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T10:29:00.612+07:00  INFO 14574 --- [qtp1661376917-79] c.d.f.s.report.PerformanceReportLogic    : Retrieved 21 records
2025-09-17T10:29:00.611+07:00  INFO 14574 --- [qtp1661376917-34] c.d.f.s.report.PerformanceReportLogic    : Retrieved 21 records
2025-09-17T10:29:05.535+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:29:47.825+07:00  INFO 14574 --- [qtp1661376917-71] n.d.module.session.ClientSessionManager  : Add a client session id = node01jdk4ev6z4y09gsszvroni0w80, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:29:47.827+07:00  INFO 14574 --- [qtp1661376917-130] n.d.module.session.ClientSessionManager  : Add a client session id = node01jdk4ev6z4y09gsszvroni0w80, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:29:47.849+07:00  INFO 14574 --- [qtp1661376917-130] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:29:47.849+07:00  INFO 14574 --- [qtp1661376917-71] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:29:55.641+07:00  INFO 14574 --- [qtp1661376917-72] n.d.module.session.ClientSessionManager  : Add a client session id = node01jdk4ev6z4y09gsszvroni0w80, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:29:55.642+07:00  INFO 14574 --- [qtp1661376917-130] n.d.module.session.ClientSessionManager  : Add a client session id = node01jdk4ev6z4y09gsszvroni0w80, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:29:55.653+07:00  INFO 14574 --- [qtp1661376917-130] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:29:55.653+07:00  INFO 14574 --- [qtp1661376917-72] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:30:03.643+07:00  INFO 14574 --- [qtp1661376917-72] n.d.module.session.ClientSessionManager  : Add a client session id = node01jdk4ev6z4y09gsszvroni0w80, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:30:03.644+07:00  INFO 14574 --- [qtp1661376917-130] n.d.module.session.ClientSessionManager  : Add a client session id = node01jdk4ev6z4y09gsszvroni0w80, token = e55833b4c7c868faf8a99b7fa6309059
2025-09-17T10:30:03.740+07:00  INFO 14574 --- [qtp1661376917-130] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:30:03.761+07:00  INFO 14574 --- [qtp1661376917-72] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:30:06.648+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:30:06.649+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T10:30:06.650+07:00  INFO 14574 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 17/09/2025@10:30:06+0700
2025-09-17T10:30:06.694+07:00  INFO 14574 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 17/09/2025@10:30:00+0700 to 17/09/2025@10:45:00+0700
2025-09-17T10:30:06.695+07:00  INFO 14574 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 17/09/2025@10:30:00+0700 to 17/09/2025@10:45:00+0700
2025-09-17T10:30:06.695+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-17T10:30:49.833+07:00  INFO 14574 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 0
2025-09-17T10:30:49.857+07:00  INFO 14574 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T10:31:04.890+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:32:06.990+07:00  INFO 14574 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:32:24.028+07:00  INFO 14574 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@75a0e939{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-17T10:32:24.030+07:00  INFO 14574 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-17T10:32:24.030+07:00  INFO 14574 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-17T10:32:24.030+07:00  INFO 14574 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-17T10:32:24.031+07:00  INFO 14574 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-17T10:32:24.032+07:00  INFO 14574 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-17T10:32:24.032+07:00  INFO 14574 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-17T10:32:24.032+07:00  INFO 14574 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-17T10:32:24.032+07:00  INFO 14574 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-17T10:32:24.032+07:00  INFO 14574 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-17T10:32:24.032+07:00  INFO 14574 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-17T10:32:24.032+07:00  INFO 14574 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-17T10:32:24.032+07:00  INFO 14574 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-17T10:32:24.032+07:00  INFO 14574 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-17T10:32:24.032+07:00  INFO 14574 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-17T10:32:24.032+07:00  INFO 14574 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-17T10:32:24.060+07:00  INFO 14574 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T10:32:24.129+07:00  INFO 14574 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-17T10:32:24.135+07:00  INFO 14574 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-17T10:32:24.150+07:00  INFO 14574 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-17T10:32:24.151+07:00  INFO 14574 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-17T10:32:24.152+07:00  INFO 14574 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-17T10:32:24.153+07:00  INFO 14574 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-17T10:32:24.153+07:00  INFO 14574 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-17T10:32:24.153+07:00  INFO 14574 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-17T10:32:24.154+07:00  INFO 14574 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-17T10:32:24.154+07:00  INFO 14574 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-17T10:32:24.154+07:00  INFO 14574 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-17T10:32:24.154+07:00  INFO 14574 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-17T10:32:24.154+07:00  INFO 14574 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-17T10:32:24.154+07:00  INFO 14574 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-17T10:32:24.154+07:00  INFO 14574 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-17T10:32:24.156+07:00  INFO 14574 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@3787eac3{STOPPING}[12.0.15,sto=0]
2025-09-17T10:32:24.159+07:00  INFO 14574 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-17T10:32:24.160+07:00  INFO 14574 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@37846cc2{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.16752132726298827578/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@55e8522e{STOPPED}}
