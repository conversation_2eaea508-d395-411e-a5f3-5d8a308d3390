2025-09-17T10:37:18.593+07:00  INFO 20257 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 20257 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-17T10:37:18.595+07:00  INFO 20257 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-17T10:37:19.961+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T10:37:20.086+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 113 ms. Found 22 JPA repository interfaces.
2025-09-17T10:37:20.113+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T10:37:20.122+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 1 JPA repository interface.
2025-09-17T10:37:20.123+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T10:37:20.143+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 19 ms. Found 10 JPA repository interfaces.
2025-09-17T10:37:20.145+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T10:37:20.204+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 59 ms. Found 3 JPA repository interfaces.
2025-09-17T10:37:20.216+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T10:37:20.224+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 1 JPA repository interface.
2025-09-17T10:37:20.240+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T10:37:20.243+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 2 JPA repository interfaces.
2025-09-17T10:37:20.243+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T10:37:20.249+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-09-17T10:37:20.255+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T10:37:20.262+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 5 JPA repository interfaces.
2025-09-17T10:37:20.269+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T10:37:20.275+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 3 JPA repository interfaces.
2025-09-17T10:37:20.275+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T10:37:20.275+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-17T10:37:20.276+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T10:37:20.286+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 10 JPA repository interfaces.
2025-09-17T10:37:20.292+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T10:37:20.295+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 3 JPA repository interfaces.
2025-09-17T10:37:20.301+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T10:37:20.306+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-09-17T10:37:20.306+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T10:37:20.314+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-09-17T10:37:20.315+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T10:37:20.323+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 4 JPA repository interfaces.
2025-09-17T10:37:20.323+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T10:37:20.324+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-17T10:37:20.325+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T10:37:20.327+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-17T10:37:20.327+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T10:37:20.333+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 7 JPA repository interfaces.
2025-09-17T10:37:20.335+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T10:37:20.337+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 2 JPA repository interfaces.
2025-09-17T10:37:20.338+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T10:37:20.338+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-17T10:37:20.338+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T10:37:20.353+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 14 ms. Found 19 JPA repository interfaces.
2025-09-17T10:37:20.366+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T10:37:20.374+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 8 JPA repository interfaces.
2025-09-17T10:37:20.374+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T10:37:20.378+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-09-17T10:37:20.378+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T10:37:20.383+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 7 JPA repository interfaces.
2025-09-17T10:37:20.384+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T10:37:20.390+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-09-17T10:37:20.390+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T10:37:20.395+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-09-17T10:37:20.396+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T10:37:20.412+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 15 ms. Found 13 JPA repository interfaces.
2025-09-17T10:37:20.412+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T10:37:20.422+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 14 JPA repository interfaces.
2025-09-17T10:37:20.423+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T10:37:20.444+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 21 ms. Found 24 JPA repository interfaces.
2025-09-17T10:37:20.445+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T10:37:20.447+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-09-17T10:37:20.456+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T10:37:20.457+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-17T10:37:20.457+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T10:37:20.477+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 19 ms. Found 12 JPA repository interfaces.
2025-09-17T10:37:20.479+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T10:37:20.531+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 51 ms. Found 66 JPA repository interfaces.
2025-09-17T10:37:20.532+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T10:37:20.534+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-17T10:37:20.541+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T10:37:20.545+07:00  INFO 20257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 4 JPA repository interfaces.
2025-09-17T10:37:20.857+07:00  INFO 20257 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-17T10:37:20.861+07:00  INFO 20257 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-17T10:37:21.390+07:00  WARN 20257 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-17T10:37:21.650+07:00  INFO 20257 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-17T10:37:21.652+07:00  INFO 20257 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-17T10:37:21.665+07:00  INFO 20257 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-17T10:37:21.665+07:00  INFO 20257 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2881 ms
2025-09-17T10:37:21.761+07:00  WARN 20257 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-17T10:37:21.761+07:00  INFO 20257 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-17T10:37:21.897+07:00  INFO 20257 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@142f0ac1
2025-09-17T10:37:21.898+07:00  INFO 20257 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-17T10:37:21.905+07:00  WARN 20257 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-17T10:37:21.905+07:00  INFO 20257 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-17T10:37:21.910+07:00  INFO 20257 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@d4e17ec
2025-09-17T10:37:21.910+07:00  INFO 20257 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-17T10:37:21.910+07:00  WARN 20257 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-17T10:37:21.910+07:00  INFO 20257 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-17T10:37:21.918+07:00  INFO 20257 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@4ef6177c
2025-09-17T10:37:21.918+07:00  INFO 20257 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-17T10:37:21.918+07:00  WARN 20257 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-17T10:37:21.918+07:00  INFO 20257 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-17T10:37:21.926+07:00  INFO 20257 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@70a9f4b7
2025-09-17T10:37:21.926+07:00  INFO 20257 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-17T10:37:21.926+07:00  WARN 20257 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-17T10:37:21.927+07:00  INFO 20257 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-17T10:37:21.933+07:00  INFO 20257 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@8d56ddd
2025-09-17T10:37:21.933+07:00  INFO 20257 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-17T10:37:21.933+07:00  INFO 20257 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-17T10:37:22.002+07:00  INFO 20257 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-17T10:37:22.006+07:00  INFO 20257 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@78002333{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.2127789563386915141/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@69b2ed81{STARTED}}
2025-09-17T10:37:22.006+07:00  INFO 20257 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@78002333{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.2127789563386915141/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@69b2ed81{STARTED}}
2025-09-17T10:37:22.008+07:00  INFO 20257 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@7c943143{STARTING}[12.0.15,sto=0] @4353ms
2025-09-17T10:37:22.143+07:00  INFO 20257 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-17T10:37:22.197+07:00  INFO 20257 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-17T10:37:22.221+07:00  INFO 20257 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-17T10:37:22.414+07:00  INFO 20257 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-17T10:37:22.465+07:00  WARN 20257 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-17T10:37:23.505+07:00  INFO 20257 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-17T10:37:23.518+07:00  INFO 20257 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@3046057a] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-17T10:37:23.710+07:00  INFO 20257 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-17T10:37:24.090+07:00  INFO 20257 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "cloud.datatp.tms", "net.datatp.module.groovy", "net.datatp.module.company.tmpl", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "cloud.datatp.module.odoo", "net.datatp.module.core.print", "net.datatp.module.chatbot", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-09-17T10:37:24.094+07:00  INFO 20257 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-17T10:37:24.104+07:00  INFO 20257 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-17T10:37:24.106+07:00  INFO 20257 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-17T10:37:24.154+07:00  INFO 20257 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-17T10:37:24.164+07:00  WARN 20257 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-17T10:37:26.762+07:00  INFO 20257 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-17T10:37:26.763+07:00  INFO 20257 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@2cc4685a] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-17T10:37:26.999+07:00  WARN 20257 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-17T10:37:26.999+07:00  WARN 20257 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-17T10:37:27.011+07:00  WARN 20257 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-17T10:37:27.011+07:00  WARN 20257 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-17T10:37:27.027+07:00  WARN 20257 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-17T10:37:27.027+07:00  WARN 20257 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-17T10:37:27.521+07:00  INFO 20257 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-17T10:37:27.528+07:00  INFO 20257 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-17T10:37:27.530+07:00  INFO 20257 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-17T10:37:27.549+07:00  INFO 20257 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-17T10:37:27.553+07:00  WARN 20257 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-17T10:37:28.117+07:00  INFO 20257 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-17T10:37:28.118+07:00  INFO 20257 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@360c748a] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-17T10:37:28.200+07:00  WARN 20257 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-17T10:37:28.200+07:00  WARN 20257 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-17T10:37:28.527+07:00  INFO 20257 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-17T10:37:28.560+07:00  INFO 20257 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-17T10:37:28.564+07:00  INFO 20257 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-17T10:37:28.564+07:00  INFO 20257 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T10:37:28.570+07:00  WARN 20257 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-17T10:37:28.696+07:00  INFO 20257 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-17T10:37:29.224+07:00  INFO 20257 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-17T10:37:29.227+07:00  INFO 20257 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-17T10:37:29.267+07:00  INFO 20257 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-17T10:37:29.314+07:00  INFO 20257 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-17T10:37:29.381+07:00  INFO 20257 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-17T10:37:29.422+07:00  INFO 20257 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-17T10:37:29.445+07:00  INFO 20257 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 147229685ms : this is harmless.
2025-09-17T10:37:29.455+07:00  INFO 20257 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-17T10:37:29.458+07:00  INFO 20257 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-17T10:37:29.471+07:00  INFO 20257 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 438735640ms : this is harmless.
2025-09-17T10:37:29.472+07:00  INFO 20257 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-17T10:37:29.486+07:00  INFO 20257 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-17T10:37:29.487+07:00  INFO 20257 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-17T10:37:31.602+07:00  INFO 20257 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-17T10:37:31.602+07:00  INFO 20257 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T10:37:31.602+07:00  WARN 20257 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-17T10:37:31.954+07:00  INFO 20257 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 17/09/2025@10:30:00+0700 to 17/09/2025@10:45:00+0700
2025-09-17T10:37:31.955+07:00  INFO 20257 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 17/09/2025@10:30:00+0700 to 17/09/2025@10:45:00+0700
2025-09-17T10:37:32.530+07:00  INFO 20257 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-17T10:37:32.530+07:00  INFO 20257 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T10:37:32.530+07:00  WARN 20257 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-17T10:37:32.829+07:00  INFO 20257 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-17T10:37:32.830+07:00  INFO 20257 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-17T10:37:32.830+07:00  INFO 20257 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-17T10:37:32.830+07:00  INFO 20257 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-17T10:37:32.830+07:00  INFO 20257 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-17T10:37:34.756+07:00  WARN 20257 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 33ca8a6c-78e4-4cc0-82d2-1b43a2fe8ce1

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-17T10:37:34.759+07:00  INFO 20257 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-17T10:37:35.059+07:00  INFO 20257 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-17T10:37:35.060+07:00  INFO 20257 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-17T10:37:35.060+07:00  INFO 20257 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-17T10:37:35.060+07:00  INFO 20257 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-17T10:37:35.060+07:00  INFO 20257 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-17T10:37:35.060+07:00  INFO 20257 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-17T10:37:35.060+07:00  INFO 20257 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-17T10:37:35.060+07:00  INFO 20257 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-17T10:37:35.060+07:00  INFO 20257 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-17T10:37:35.060+07:00  INFO 20257 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-17T10:37:35.060+07:00  INFO 20257 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-17T10:37:35.060+07:00  INFO 20257 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-17T10:37:35.061+07:00  INFO 20257 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-17T10:37:35.061+07:00  INFO 20257 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-17T10:37:35.061+07:00  INFO 20257 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-17T10:37:35.128+07:00  INFO 20257 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-17T10:37:35.128+07:00  INFO 20257 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-17T10:37:35.129+07:00  INFO 20257 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-09-17T10:37:35.137+07:00  INFO 20257 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@5a1ae413{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-17T10:37:35.138+07:00  INFO 20257 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-17T10:37:35.139+07:00  INFO 20257 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-17T10:37:35.180+07:00  INFO 20257 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-17T10:37:35.180+07:00  INFO 20257 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-17T10:37:35.186+07:00  INFO 20257 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 17.08 seconds (process running for 17.53)
2025-09-17T10:37:41.813+07:00  INFO 20257 --- [qtp1501506879-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01vlb1af518u411v1ectgkkpw10
2025-09-17T10:37:42.120+07:00  INFO 20257 --- [qtp1501506879-35] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:37:42.246+07:00  INFO 20257 --- [qtp1501506879-37] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0yt93nu7m0efhlueiz28garfy1
2025-09-17T10:37:42.415+07:00  INFO 20257 --- [qtp1501506879-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0yt93nu7m0efhlueiz28garfy1, token = b8435d5e4a74ef1e7aa7bd8604ce8322
2025-09-17T10:37:42.519+07:00  INFO 20257 --- [qtp1501506879-35] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:37:42.524+07:00  INFO 20257 --- [qtp1501506879-37] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:38:03.153+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:38:05.575+07:00  INFO 20257 --- [qtp1501506879-37] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-09-17T10:38:38.289+07:00  INFO 20257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 0
2025-09-17T10:38:38.336+07:00  INFO 20257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T10:39:06.378+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:39:08.551+07:00  INFO 20257 --- [qtp1501506879-35] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:39:08.559+07:00  INFO 20257 --- [qtp1501506879-35] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:39:08.580+07:00  INFO 20257 --- [qtp1501506879-62] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:39:08.584+07:00  INFO 20257 --- [qtp1501506879-62] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:40:02.465+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:40:02.469+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T10:40:28.003+07:00  INFO 20257 --- [qtp1501506879-40] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:40:28.007+07:00  INFO 20257 --- [qtp1501506879-60] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:40:28.020+07:00  INFO 20257 --- [qtp1501506879-60] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:40:28.020+07:00  INFO 20257 --- [qtp1501506879-40] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:40:28.515+07:00  INFO 20257 --- [qtp1501506879-60] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:40:28.523+07:00  INFO 20257 --- [qtp1501506879-60] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:40:28.532+07:00  INFO 20257 --- [qtp1501506879-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:40:28.538+07:00  INFO 20257 --- [qtp1501506879-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:40:37.540+07:00  INFO 20257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T10:40:37.553+07:00  INFO 20257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T10:41:05.600+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:41:33.152+07:00  INFO 20257 --- [qtp1501506879-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:41:33.165+07:00  INFO 20257 --- [qtp1501506879-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:41:33.178+07:00  INFO 20257 --- [qtp1501506879-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:41:33.182+07:00  INFO 20257 --- [qtp1501506879-39] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:41:33.554+07:00  INFO 20257 --- [qtp1501506879-41] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:41:33.557+07:00  INFO 20257 --- [qtp1501506879-34] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:41:33.565+07:00  INFO 20257 --- [qtp1501506879-41] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:41:33.566+07:00  INFO 20257 --- [qtp1501506879-34] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:41:39.638+07:00  INFO 20257 --- [qtp1501506879-63] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:41:39.667+07:00  INFO 20257 --- [qtp1501506879-63] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:41:39.673+07:00  INFO 20257 --- [qtp1501506879-72] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:41:39.738+07:00  INFO 20257 --- [qtp1501506879-72] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:41:40.132+07:00  INFO 20257 --- [qtp1501506879-37] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:41:40.135+07:00  INFO 20257 --- [qtp1501506879-60] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:41:40.142+07:00  INFO 20257 --- [qtp1501506879-37] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:41:40.146+07:00  INFO 20257 --- [qtp1501506879-60] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:41:42.515+07:00  INFO 20257 --- [qtp1501506879-62] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T10:41:42.515+07:00  INFO 20257 --- [qtp1501506879-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T10:41:42.651+07:00  INFO 20257 --- [qtp1501506879-37] c.d.f.s.report.PerformanceReportLogic    : Retrieved 17 records
2025-09-17T10:41:42.651+07:00  INFO 20257 --- [qtp1501506879-62] c.d.f.s.report.PerformanceReportLogic    : Retrieved 17 records
2025-09-17T10:41:42.665+07:00  INFO 20257 --- [qtp1501506879-60] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T10:41:42.666+07:00  INFO 20257 --- [qtp1501506879-63] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T10:41:43.179+07:00  INFO 20257 --- [qtp1501506879-60] c.d.f.s.report.PerformanceReportLogic    : Retrieved 21 records
2025-09-17T10:41:43.183+07:00  INFO 20257 --- [qtp1501506879-63] c.d.f.s.report.PerformanceReportLogic    : Retrieved 21 records
2025-09-17T10:42:06.715+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:42:41.815+07:00  INFO 20257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 0
2025-09-17T10:42:41.834+07:00  INFO 20257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T10:43:04.886+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:44:06.013+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:44:16.546+07:00  INFO 20257 --- [qtp1501506879-60] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:44:16.547+07:00  INFO 20257 --- [qtp1501506879-40] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:44:16.555+07:00  INFO 20257 --- [qtp1501506879-60] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:44:16.555+07:00  INFO 20257 --- [qtp1501506879-40] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:44:16.577+07:00  INFO 20257 --- [qtp1501506879-72] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:44:16.578+07:00  INFO 20257 --- [qtp1501506879-41] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:44:16.581+07:00  INFO 20257 --- [qtp1501506879-41] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:44:16.581+07:00  INFO 20257 --- [qtp1501506879-72] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:44:37.380+07:00  INFO 20257 --- [qtp1501506879-72] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:44:37.395+07:00  INFO 20257 --- [qtp1501506879-72] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:44:37.441+07:00  INFO 20257 --- [qtp1501506879-70] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:44:37.461+07:00  INFO 20257 --- [qtp1501506879-70] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:44:37.538+07:00  INFO 20257 --- [qtp1501506879-72] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:44:37.541+07:00  INFO 20257 --- [qtp1501506879-62] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:44:37.551+07:00  INFO 20257 --- [qtp1501506879-72] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:44:37.554+07:00  INFO 20257 --- [qtp1501506879-62] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:44:42.122+07:00  INFO 20257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-17T10:44:42.132+07:00  INFO 20257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T10:44:56.011+07:00  INFO 20257 --- [qtp1501506879-70] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:44:56.023+07:00  INFO 20257 --- [qtp1501506879-70] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:44:56.042+07:00  INFO 20257 --- [qtp1501506879-67] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:44:56.073+07:00  INFO 20257 --- [qtp1501506879-67] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:44:56.529+07:00  INFO 20257 --- [qtp1501506879-70] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:44:56.531+07:00  INFO 20257 --- [qtp1501506879-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:44:56.535+07:00  INFO 20257 --- [qtp1501506879-70] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:44:56.537+07:00  INFO 20257 --- [qtp1501506879-39] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:45:04.170+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:45:04.176+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T10:45:04.179+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-17T10:45:04.188+07:00  INFO 20257 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 17/09/2025@10:45:04+0700
2025-09-17T10:45:04.224+07:00  INFO 20257 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 17/09/2025@10:45:00+0700 to 17/09/2025@11:00:00+0700
2025-09-17T10:45:04.225+07:00  INFO 20257 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 17/09/2025@10:45:00+0700 to 17/09/2025@11:00:00+0700
2025-09-17T10:46:06.369+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:46:41.452+07:00  INFO 20257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 0
2025-09-17T10:46:41.458+07:00  INFO 20257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T10:46:57.206+07:00  INFO 20257 --- [qtp1501506879-67] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:46:57.207+07:00  INFO 20257 --- [qtp1501506879-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:46:57.217+07:00  INFO 20257 --- [qtp1501506879-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:46:57.217+07:00  INFO 20257 --- [qtp1501506879-67] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:46:57.996+07:00  INFO 20257 --- [qtp1501506879-67] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:46:58.013+07:00  INFO 20257 --- [qtp1501506879-67] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:46:58.096+07:00  INFO 20257 --- [qtp1501506879-77] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:46:58.124+07:00  INFO 20257 --- [qtp1501506879-77] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:47:03.497+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:48:06.601+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:48:22.033+07:00  INFO 20257 --- [Scheduler-75629578-1] n.d.m.session.AppHttpSessionListener     : The session node0yt93nu7m0efhlueiz28garfy1 is destroyed.
2025-09-17T10:48:37.013+07:00  INFO 20257 --- [qtp1501506879-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:48:37.017+07:00  INFO 20257 --- [qtp1501506879-77] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:48:37.029+07:00  INFO 20257 --- [qtp1501506879-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:48:37.029+07:00  INFO 20257 --- [qtp1501506879-77] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:48:37.542+07:00  INFO 20257 --- [qtp1501506879-77] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:48:37.544+07:00  INFO 20257 --- [qtp1501506879-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:48:37.547+07:00  INFO 20257 --- [qtp1501506879-77] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:48:37.554+07:00  INFO 20257 --- [qtp1501506879-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:48:41.676+07:00  INFO 20257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 2
2025-09-17T10:48:41.692+07:00  INFO 20257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T10:48:56.140+07:00  INFO 20257 --- [qtp1501506879-67] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:48:56.158+07:00  INFO 20257 --- [qtp1501506879-67] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:48:56.173+07:00  INFO 20257 --- [qtp1501506879-66] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:48:56.178+07:00  INFO 20257 --- [qtp1501506879-66] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:48:56.524+07:00  INFO 20257 --- [qtp1501506879-77] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:48:56.525+07:00  INFO 20257 --- [qtp1501506879-66] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:48:56.529+07:00  INFO 20257 --- [qtp1501506879-66] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:48:56.529+07:00  INFO 20257 --- [qtp1501506879-77] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:49:02.730+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:49:19.394+07:00  INFO 20257 --- [qtp1501506879-34] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:49:19.396+07:00  INFO 20257 --- [qtp1501506879-67] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:49:19.402+07:00  INFO 20257 --- [qtp1501506879-67] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:49:19.402+07:00  INFO 20257 --- [qtp1501506879-34] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:49:19.520+07:00  INFO 20257 --- [qtp1501506879-67] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:49:19.520+07:00  INFO 20257 --- [qtp1501506879-62] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:49:19.525+07:00  INFO 20257 --- [qtp1501506879-67] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:49:19.525+07:00  INFO 20257 --- [qtp1501506879-62] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:49:49.080+07:00  INFO 20257 --- [qtp1501506879-34] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:49:49.090+07:00  INFO 20257 --- [qtp1501506879-34] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:49:49.163+07:00  INFO 20257 --- [qtp1501506879-68] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:49:49.171+07:00  INFO 20257 --- [qtp1501506879-68] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:49:49.522+07:00  INFO 20257 --- [qtp1501506879-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:49:49.523+07:00  INFO 20257 --- [qtp1501506879-67] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:49:49.532+07:00  INFO 20257 --- [qtp1501506879-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:49:49.532+07:00  INFO 20257 --- [qtp1501506879-67] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:50:05.841+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:50:05.847+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T10:50:24.810+07:00  INFO 20257 --- [qtp1501506879-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:50:24.811+07:00  INFO 20257 --- [qtp1501506879-35] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:50:24.817+07:00  INFO 20257 --- [qtp1501506879-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:50:24.820+07:00  INFO 20257 --- [qtp1501506879-35] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:50:25.541+07:00  INFO 20257 --- [qtp1501506879-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:50:25.543+07:00  INFO 20257 --- [qtp1501506879-77] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:50:25.547+07:00  INFO 20257 --- [qtp1501506879-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:50:25.548+07:00  INFO 20257 --- [qtp1501506879-77] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:50:40.908+07:00  INFO 20257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T10:50:40.916+07:00  INFO 20257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T10:50:49.573+07:00  INFO 20257 --- [qtp1501506879-66] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:50:49.575+07:00  INFO 20257 --- [qtp1501506879-77] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:50:49.589+07:00  INFO 20257 --- [qtp1501506879-77] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:50:49.589+07:00  INFO 20257 --- [qtp1501506879-66] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:50:49.749+07:00  INFO 20257 --- [qtp1501506879-60] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:50:49.749+07:00  INFO 20257 --- [qtp1501506879-77] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:50:49.755+07:00  INFO 20257 --- [qtp1501506879-60] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:50:49.756+07:00  INFO 20257 --- [qtp1501506879-77] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:51:06.959+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:52:05.061+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:52:40.176+07:00  INFO 20257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 1
2025-09-17T10:52:40.193+07:00  INFO 20257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T10:53:06.233+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:53:15.575+07:00  INFO 20257 --- [qtp1501506879-68] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:53:15.577+07:00  INFO 20257 --- [qtp1501506879-66] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:53:15.577+07:00  INFO 20257 --- [qtp1501506879-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:53:15.607+07:00  INFO 20257 --- [qtp1501506879-66] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:53:15.607+07:00  INFO 20257 --- [qtp1501506879-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:53:15.607+07:00  INFO 20257 --- [qtp1501506879-68] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:53:15.619+07:00  INFO 20257 --- [qtp1501506879-69] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:53:15.624+07:00  INFO 20257 --- [qtp1501506879-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:54:04.311+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:54:39.371+07:00  INFO 20257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 2
2025-09-17T10:54:39.377+07:00  INFO 20257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T10:55:06.426+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T10:55:06.428+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:56:03.520+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:56:38.608+07:00  INFO 20257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 5
2025-09-17T10:56:38.635+07:00  INFO 20257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T10:57:06.686+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:58:02.779+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:58:37.832+07:00  INFO 20257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T10:58:37.836+07:00  INFO 20257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T10:59:05.881+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T10:59:31.961+07:00  INFO 20257 --- [qtp1501506879-68] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:59:32.006+07:00  INFO 20257 --- [qtp1501506879-68] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:59:32.021+07:00  INFO 20257 --- [qtp1501506879-78] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:59:32.029+07:00  INFO 20257 --- [qtp1501506879-78] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:59:46.838+07:00  INFO 20257 --- [qtp1501506879-62] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:59:46.866+07:00  INFO 20257 --- [qtp1501506879-62] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T10:59:46.889+07:00  INFO 20257 --- [qtp1501506879-100] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T10:59:46.916+07:00  INFO 20257 --- [qtp1501506879-100] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:00:06.967+07:00  INFO 20257 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 17/09/2025@11:00:06+0700
2025-09-17T11:00:06.991+07:00  INFO 20257 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 17/09/2025@11:00:00+0700 to 17/09/2025@11:15:00+0700
2025-09-17T11:00:06.991+07:00  INFO 20257 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 17/09/2025@11:00:00+0700 to 17/09/2025@11:15:00+0700
2025-09-17T11:00:06.992+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-17T11:00:06.994+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-09-17T11:00:06.994+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:00:06.994+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T11:00:42.080+07:00  INFO 20257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-09-17T11:00:42.100+07:00  INFO 20257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T11:01:05.134+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:02:06.235+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:02:28.801+07:00  INFO 20257 --- [qtp1501506879-100] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:02:28.802+07:00  INFO 20257 --- [qtp1501506879-69] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:02:28.809+07:00  INFO 20257 --- [qtp1501506879-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:02:28.810+07:00  INFO 20257 --- [qtp1501506879-100] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:02:41.367+07:00  INFO 20257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T11:02:41.380+07:00  INFO 20257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T11:03:04.418+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:04:06.536+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:04:41.625+07:00  INFO 20257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-17T11:04:41.640+07:00  INFO 20257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T11:05:03.680+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:05:03.682+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T11:06:06.774+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:06:20.144+07:00  INFO 20257 --- [qtp1501506879-69] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:06:20.145+07:00  INFO 20257 --- [qtp1501506879-67] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:06:20.152+07:00  INFO 20257 --- [qtp1501506879-67] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:06:20.152+07:00  INFO 20257 --- [qtp1501506879-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:06:41.864+07:00  INFO 20257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-17T11:06:41.880+07:00  INFO 20257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T11:07:02.905+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:08:06.026+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:08:41.089+07:00  INFO 20257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T11:08:41.100+07:00  INFO 20257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T11:09:02.130+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:09:04.553+07:00  INFO 20257 --- [qtp1501506879-100] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:09:04.594+07:00  INFO 20257 --- [qtp1501506879-134] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:09:04.646+07:00  INFO 20257 --- [qtp1501506879-100] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:09:04.662+07:00  INFO 20257 --- [qtp1501506879-134] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:10:05.204+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:10:05.209+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T11:10:40.299+07:00  INFO 20257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 12, expire count 1
2025-09-17T11:10:40.314+07:00  INFO 20257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-17T11:10:59.900+07:00  INFO 20257 --- [qtp1501506879-134] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:10:59.953+07:00  INFO 20257 --- [qtp1501506879-136] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:10:59.955+07:00  INFO 20257 --- [qtp1501506879-134] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:10:59.956+07:00  INFO 20257 --- [qtp1501506879-136] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:11:06.347+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:12:04.439+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:12:33.126+07:00  INFO 20257 --- [qtp1501506879-134] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:12:33.127+07:00  INFO 20257 --- [qtp1501506879-63] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:12:33.135+07:00  INFO 20257 --- [qtp1501506879-134] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:12:33.135+07:00  INFO 20257 --- [qtp1501506879-63] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:12:39.517+07:00  INFO 20257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-17T11:12:39.520+07:00  INFO 20257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T11:13:06.570+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:13:24.499+07:00  INFO 20257 --- [qtp1501506879-63] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:13:24.500+07:00  INFO 20257 --- [qtp1501506879-134] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:13:24.524+07:00  INFO 20257 --- [qtp1501506879-63] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:13:24.524+07:00  INFO 20257 --- [qtp1501506879-134] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:13:56.821+07:00  INFO 20257 --- [qtp1501506879-134] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:13:56.821+07:00  INFO 20257 --- [qtp1501506879-68] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:13:56.840+07:00  INFO 20257 --- [qtp1501506879-68] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:13:56.847+07:00  INFO 20257 --- [qtp1501506879-134] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:13:59.895+07:00  INFO 20257 --- [qtp1501506879-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:13:59.906+07:00  INFO 20257 --- [qtp1501506879-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:13:59.926+07:00  INFO 20257 --- [qtp1501506879-126] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:13:59.933+07:00  INFO 20257 --- [qtp1501506879-126] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:14:03.696+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:14:25.551+07:00  INFO 20257 --- [qtp1501506879-100] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:14:25.551+07:00  INFO 20257 --- [qtp1501506879-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:14:25.560+07:00  INFO 20257 --- [qtp1501506879-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:14:25.560+07:00  INFO 20257 --- [qtp1501506879-100] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:14:38.783+07:00  INFO 20257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-17T11:14:38.800+07:00  INFO 20257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T11:15:06.844+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-17T11:15:06.847+07:00  INFO 20257 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 17/09/2025@11:15:06+0700
2025-09-17T11:15:06.863+07:00  INFO 20257 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 17/09/2025@11:15:00+0700 to 17/09/2025@11:30:00+0700
2025-09-17T11:15:06.863+07:00  INFO 20257 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 17/09/2025@11:15:00+0700 to 17/09/2025@11:30:00+0700
2025-09-17T11:15:06.863+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T11:15:06.864+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:15:16.274+07:00  INFO 20257 --- [qtp1501506879-134] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:15:16.278+07:00  INFO 20257 --- [qtp1501506879-100] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:15:16.300+07:00  INFO 20257 --- [qtp1501506879-100] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:15:16.300+07:00  INFO 20257 --- [qtp1501506879-134] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:16:02.961+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:16:22.281+07:00  INFO 20257 --- [qtp1501506879-126] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:16:22.282+07:00  INFO 20257 --- [qtp1501506879-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:16:22.292+07:00  INFO 20257 --- [qtp1501506879-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:16:22.292+07:00  INFO 20257 --- [qtp1501506879-126] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:16:29.997+07:00  INFO 20257 --- [qtp1501506879-100] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:16:29.998+07:00  INFO 20257 --- [qtp1501506879-37] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:16:30.003+07:00  INFO 20257 --- [qtp1501506879-100] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:16:30.003+07:00  INFO 20257 --- [qtp1501506879-37] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:16:38.023+07:00  INFO 20257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T11:16:38.028+07:00  INFO 20257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T11:17:06.076+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:18:02.175+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:18:37.261+07:00  INFO 20257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-17T11:18:37.274+07:00  INFO 20257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T11:19:05.317+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:20:06.404+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T11:20:06.405+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:20:41.462+07:00  INFO 20257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 12
2025-09-17T11:20:41.467+07:00  INFO 20257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T11:21:04.511+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:22:06.597+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:22:41.673+07:00  INFO 20257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T11:22:41.679+07:00  INFO 20257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T11:23:03.716+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:24:06.807+07:00  INFO 20257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:24:20.325+07:00  INFO 20257 --- [qtp1501506879-126] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T11:24:20.325+07:00  INFO 20257 --- [qtp1501506879-172] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T11:24:20.742+07:00  INFO 20257 --- [qtp1501506879-172] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-17T11:24:20.742+07:00  INFO 20257 --- [qtp1501506879-126] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-17T11:24:33.648+07:00  INFO 20257 --- [qtp1501506879-132] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:24:33.659+07:00  INFO 20257 --- [qtp1501506879-132] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:24:33.672+07:00  INFO 20257 --- [qtp1501506879-103] n.d.module.session.ClientSessionManager  : Add a client session id = node01vlb1af518u411v1ectgkkpw10, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:24:33.680+07:00  INFO 20257 --- [qtp1501506879-103] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:24:33.791+07:00  INFO 20257 --- [qtp1501506879-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T11:24:33.791+07:00  INFO 20257 --- [qtp1501506879-172] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T11:24:33.794+07:00  INFO 20257 --- [qtp1501506879-172] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-17T11:24:33.794+07:00  INFO 20257 --- [qtp1501506879-37] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-17T11:24:37.180+07:00 ERROR 20257 --- [qtp1501506879-132] n.d.m.monitor.call.EndpointCallContext   : Cannot find the component = class cloud.datatp.fforwarder.core.partner.CRMPartnerService, mehthod = saveCRMPartner, userParams = 
 {
  "entity" : {
    "id" : 56638,
    "editState" : "ORIGIN",
    "loadState" : "FromDB",
    "version" : 1,
    "createdBy" : "sandy.vnhph",
    "createdTime" : "17/09/2025@03:02:20+0000",
    "modifiedBy" : "sandy.vnhph",
    "modifiedTime" : "17/09/2025@03:27:48+0000",
    "storageState" : "ACTIVE",
    "accountId" : 1687,
    "partnerCode" : "AG000019",
    "partnerCodeTemp" : "AG000019",
    "status" : "NEW",
    "partnerGroup" : "AGENTS",
    "category" : "AGENT_OVERSEAS",
    "name" : "TEST2222",
    "label" : "TEST2222",
    "localizedLabel" : "TEST2222",
    "personalContact" : "TETS",
    "email" : "<EMAIL>",
    "fax" : "",
    "cell" : "**********",
    "source" : "A2B2A (not a member of associations in this list, Agent and Bee have reciprocal nominations)",
    "industryCode" : "I001",
    "industryLabel" : "Agriculture; plantations;other rural sectors ",
    "countryId" : 244,
    "countryLabel" : "VIETNAM",
    "address" : "TEST12312312312",
    "localizedAddress" : "TEST12312312312",
    "printCustomConfirmBillInfo" : "TEST2222\nTEST12312312312\nTETS\nTEL :**********",
    "note" : "ádfasdf",
    "inputUsername" : "SANDY.VNHPH",
    "scope" : "Overseas",
    "refund" : false,
    "requestSalemanAccountId" : 11174,
    "requestSalemanLabel" : "ĐINH THỊ THANH MINH"
  }
}
2025-09-17T11:24:37.189+07:00 ERROR 20257 --- [qtp1501506879-132] n.d.m.monitor.call.EndpointCallService   : Unknown Error when handle the endpoint CRMPartnerService/saveCRMPartner
2025-09-17T11:24:37.189+07:00 ERROR 20257 --- [qtp1501506879-132] n.d.m.monitor.call.EndpointCallService   : Exception: 

net.datatp.util.error.RuntimeError: Cannot find the component class cloud.datatp.fforwarder.core.partner.CRMPartnerService, mehtod saveCRMPartner
	at net.datatp.util.error.RuntimeError.IllegalArgument(RuntimeError.java:61)
	at net.datatp.module.monitor.call.EndpointCallContext.computeArguments(EndpointCallContext.java:96)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:150)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)

2025-09-17T11:24:41.869+07:00  INFO 20257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 0
2025-09-17T11:24:41.878+07:00  INFO 20257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T11:24:59.102+07:00  INFO 20257 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@5a1ae413{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-17T11:24:59.104+07:00  INFO 20257 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-17T11:24:59.104+07:00  INFO 20257 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-17T11:24:59.104+07:00  INFO 20257 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-17T11:24:59.105+07:00  INFO 20257 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-17T11:24:59.105+07:00  INFO 20257 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-17T11:24:59.105+07:00  INFO 20257 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-17T11:24:59.105+07:00  INFO 20257 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-17T11:24:59.105+07:00  INFO 20257 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-17T11:24:59.105+07:00  INFO 20257 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-17T11:24:59.105+07:00  INFO 20257 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-17T11:24:59.105+07:00  INFO 20257 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-17T11:24:59.105+07:00  INFO 20257 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-17T11:24:59.105+07:00  INFO 20257 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-17T11:24:59.105+07:00  INFO 20257 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-17T11:24:59.105+07:00  INFO 20257 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-17T11:24:59.145+07:00  INFO 20257 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T11:24:59.235+07:00  INFO 20257 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-17T11:24:59.241+07:00  INFO 20257 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-17T11:24:59.268+07:00  INFO 20257 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-17T11:24:59.283+07:00  INFO 20257 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-17T11:24:59.314+07:00  INFO 20257 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-17T11:24:59.315+07:00  INFO 20257 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-17T11:24:59.316+07:00  INFO 20257 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-17T11:24:59.317+07:00  INFO 20257 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-17T11:24:59.317+07:00  INFO 20257 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-17T11:24:59.317+07:00  INFO 20257 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-17T11:24:59.317+07:00  INFO 20257 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-17T11:24:59.317+07:00  INFO 20257 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-17T11:24:59.317+07:00  INFO 20257 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-17T11:24:59.317+07:00  INFO 20257 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-17T11:24:59.318+07:00  INFO 20257 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-17T11:24:59.324+07:00  INFO 20257 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@7c943143{STOPPING}[12.0.15,sto=0]
2025-09-17T11:24:59.329+07:00  INFO 20257 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-17T11:24:59.330+07:00  INFO 20257 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@78002333{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.2127789563386915141/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@69b2ed81{STOPPED}}
