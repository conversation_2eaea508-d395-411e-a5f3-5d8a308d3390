2025-09-17T11:25:15.560+07:00  INFO 26608 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 26608 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-17T11:25:15.561+07:00  INFO 26608 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-17T11:25:16.277+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T11:25:16.342+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 61 ms. Found 22 JPA repository interfaces.
2025-09-17T11:25:16.352+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T11:25:16.353+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-17T11:25:16.354+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T11:25:16.397+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 43 ms. Found 10 JPA repository interfaces.
2025-09-17T11:25:16.398+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T11:25:16.402+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 3 JPA repository interfaces.
2025-09-17T11:25:16.412+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T11:25:16.417+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 1 JPA repository interface.
2025-09-17T11:25:16.426+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T11:25:16.428+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-17T11:25:16.428+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T11:25:16.432+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-17T11:25:16.435+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T11:25:16.439+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-09-17T11:25:16.443+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T11:25:16.446+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-17T11:25:16.446+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T11:25:16.446+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-17T11:25:16.446+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T11:25:16.453+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 10 JPA repository interfaces.
2025-09-17T11:25:16.458+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T11:25:16.461+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-17T11:25:16.465+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T11:25:16.469+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-17T11:25:16.469+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T11:25:16.478+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 12 JPA repository interfaces.
2025-09-17T11:25:16.478+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T11:25:16.482+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-09-17T11:25:16.482+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T11:25:16.482+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-17T11:25:16.482+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T11:25:16.483+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-17T11:25:16.483+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T11:25:16.488+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-09-17T11:25:16.488+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T11:25:16.489+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-17T11:25:16.489+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T11:25:16.490+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-17T11:25:16.490+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T11:25:16.500+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 19 JPA repository interfaces.
2025-09-17T11:25:16.510+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T11:25:16.516+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 8 JPA repository interfaces.
2025-09-17T11:25:16.517+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T11:25:16.520+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-09-17T11:25:16.521+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T11:25:16.525+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-09-17T11:25:16.525+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T11:25:16.530+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-09-17T11:25:16.531+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T11:25:16.535+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-17T11:25:16.535+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T11:25:16.544+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 13 JPA repository interfaces.
2025-09-17T11:25:16.545+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T11:25:16.554+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 14 JPA repository interfaces.
2025-09-17T11:25:16.554+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T11:25:16.569+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 14 ms. Found 24 JPA repository interfaces.
2025-09-17T11:25:16.569+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T11:25:16.570+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-17T11:25:16.576+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T11:25:16.577+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-17T11:25:16.577+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T11:25:16.585+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 12 JPA repository interfaces.
2025-09-17T11:25:16.587+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T11:25:16.626+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 38 ms. Found 66 JPA repository interfaces.
2025-09-17T11:25:16.626+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T11:25:16.627+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-17T11:25:16.632+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T11:25:16.636+07:00  INFO 26608 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-09-17T11:25:16.814+07:00  INFO 26608 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-17T11:25:16.818+07:00  INFO 26608 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-17T11:25:17.102+07:00  WARN 26608 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-17T11:25:17.301+07:00  INFO 26608 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-17T11:25:17.303+07:00  INFO 26608 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-17T11:25:17.315+07:00  INFO 26608 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-17T11:25:17.315+07:00  INFO 26608 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1618 ms
2025-09-17T11:25:17.367+07:00  WARN 26608 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-17T11:25:17.367+07:00  INFO 26608 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-17T11:25:17.470+07:00  INFO 26608 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@412b90cc
2025-09-17T11:25:17.471+07:00  INFO 26608 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-17T11:25:17.476+07:00  WARN 26608 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-17T11:25:17.476+07:00  INFO 26608 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-17T11:25:17.481+07:00  INFO 26608 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@273cb729
2025-09-17T11:25:17.481+07:00  INFO 26608 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-17T11:25:17.482+07:00  WARN 26608 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-17T11:25:17.482+07:00  INFO 26608 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-17T11:25:17.490+07:00  INFO 26608 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@327fd5c9
2025-09-17T11:25:17.490+07:00  INFO 26608 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-17T11:25:17.490+07:00  WARN 26608 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-17T11:25:17.490+07:00  INFO 26608 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-17T11:25:17.500+07:00  INFO 26608 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@316745d8
2025-09-17T11:25:17.500+07:00  INFO 26608 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-17T11:25:17.500+07:00  WARN 26608 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-17T11:25:17.500+07:00  INFO 26608 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-17T11:25:17.507+07:00  INFO 26608 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@122557f8
2025-09-17T11:25:17.507+07:00  INFO 26608 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-17T11:25:17.507+07:00  INFO 26608 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-17T11:25:17.553+07:00  INFO 26608 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-17T11:25:17.601+07:00  INFO 26608 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@3566d527{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.12712423731094366303/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@ae5eeee{STARTED}}
2025-09-17T11:25:17.602+07:00  INFO 26608 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@3566d527{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.12712423731094366303/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@ae5eeee{STARTED}}
2025-09-17T11:25:17.603+07:00  INFO 26608 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@30a1b9b0{STARTING}[12.0.15,sto=0] @2637ms
2025-09-17T11:25:17.712+07:00  INFO 26608 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-17T11:25:17.754+07:00  INFO 26608 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-17T11:25:17.771+07:00  INFO 26608 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-17T11:25:17.903+07:00  INFO 26608 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-17T11:25:17.941+07:00  WARN 26608 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-17T11:25:18.545+07:00  INFO 26608 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-17T11:25:18.553+07:00  INFO 26608 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@7b4679a8] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-17T11:25:18.725+07:00  INFO 26608 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-17T11:25:18.919+07:00  INFO 26608 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.graphapi", "net.datatp.module.kpi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.company.tmpl", "cloud.datatp.tms", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.chatbot", "net.datatp.module.core.print", "cloud.datatp.module.odoo", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.zalo", "net.datatp.module.accounting" ]
2025-09-17T11:25:18.921+07:00  INFO 26608 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-17T11:25:18.928+07:00  INFO 26608 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-17T11:25:18.929+07:00  INFO 26608 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-17T11:25:18.956+07:00  INFO 26608 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-17T11:25:18.963+07:00  WARN 26608 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-17T11:25:20.969+07:00  INFO 26608 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-17T11:25:20.970+07:00  INFO 26608 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@45262cfd] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-17T11:25:21.216+07:00  WARN 26608 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-17T11:25:21.216+07:00  WARN 26608 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-17T11:25:21.227+07:00  WARN 26608 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-17T11:25:21.227+07:00  WARN 26608 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-17T11:25:21.241+07:00  WARN 26608 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-17T11:25:21.241+07:00  WARN 26608 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-17T11:25:21.689+07:00  INFO 26608 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-17T11:25:21.696+07:00  INFO 26608 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-17T11:25:21.698+07:00  INFO 26608 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-17T11:25:21.721+07:00  INFO 26608 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-17T11:25:21.725+07:00  WARN 26608 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-17T11:25:22.185+07:00  INFO 26608 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-17T11:25:22.185+07:00  INFO 26608 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@75314f50] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-17T11:25:22.271+07:00  WARN 26608 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-17T11:25:22.271+07:00  WARN 26608 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-17T11:25:22.630+07:00  INFO 26608 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-17T11:25:22.658+07:00  INFO 26608 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-17T11:25:22.662+07:00  INFO 26608 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-17T11:25:22.662+07:00  INFO 26608 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T11:25:22.668+07:00  WARN 26608 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-17T11:25:22.798+07:00  INFO 26608 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-17T11:25:23.270+07:00  INFO 26608 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-17T11:25:23.273+07:00  INFO 26608 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-17T11:25:23.307+07:00  INFO 26608 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-17T11:25:23.352+07:00  INFO 26608 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-17T11:25:23.419+07:00  INFO 26608 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-17T11:25:23.446+07:00  INFO 26608 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-17T11:25:23.470+07:00  INFO 26608 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 150384787ms : this is harmless.
2025-09-17T11:25:23.478+07:00  INFO 26608 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-17T11:25:23.481+07:00  INFO 26608 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-17T11:25:23.496+07:00  INFO 26608 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 441890747ms : this is harmless.
2025-09-17T11:25:23.497+07:00  INFO 26608 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-17T11:25:23.511+07:00  INFO 26608 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-17T11:25:23.512+07:00  INFO 26608 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-17T11:25:25.559+07:00  INFO 26608 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-17T11:25:25.559+07:00  INFO 26608 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T11:25:25.560+07:00  WARN 26608 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-17T11:25:25.874+07:00  INFO 26608 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 17/09/2025@11:15:00+0700 to 17/09/2025@11:30:00+0700
2025-09-17T11:25:25.875+07:00  INFO 26608 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 17/09/2025@11:15:00+0700 to 17/09/2025@11:30:00+0700
2025-09-17T11:25:26.471+07:00  INFO 26608 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-17T11:25:26.471+07:00  INFO 26608 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T11:25:26.472+07:00  WARN 26608 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-17T11:25:26.772+07:00  INFO 26608 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-17T11:25:26.772+07:00  INFO 26608 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-17T11:25:26.772+07:00  INFO 26608 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-17T11:25:26.772+07:00  INFO 26608 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-17T11:25:26.772+07:00  INFO 26608 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-17T11:25:28.797+07:00  WARN 26608 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: f1d7c0fd-e289-449e-b065-f424bfb1c723

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-17T11:25:28.806+07:00  INFO 26608 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-17T11:25:29.184+07:00  INFO 26608 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-17T11:25:29.185+07:00  INFO 26608 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-17T11:25:29.185+07:00  INFO 26608 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-17T11:25:29.185+07:00  INFO 26608 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-17T11:25:29.185+07:00  INFO 26608 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-17T11:25:29.185+07:00  INFO 26608 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-17T11:25:29.185+07:00  INFO 26608 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-17T11:25:29.185+07:00  INFO 26608 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-17T11:25:29.185+07:00  INFO 26608 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-17T11:25:29.185+07:00  INFO 26608 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-17T11:25:29.185+07:00  INFO 26608 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-17T11:25:29.185+07:00  INFO 26608 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-17T11:25:29.186+07:00  INFO 26608 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-17T11:25:29.186+07:00  INFO 26608 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-17T11:25:29.186+07:00  INFO 26608 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-17T11:25:29.236+07:00  INFO 26608 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-17T11:25:29.236+07:00  INFO 26608 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-17T11:25:29.238+07:00  INFO 26608 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-09-17T11:25:29.245+07:00  INFO 26608 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@38712e9c{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-17T11:25:29.246+07:00  INFO 26608 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-17T11:25:29.247+07:00  INFO 26608 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-17T11:25:29.284+07:00  INFO 26608 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-17T11:25:29.284+07:00  INFO 26608 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-17T11:25:29.290+07:00  INFO 26608 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 14.076 seconds (process running for 14.324)
2025-09-17T11:25:34.924+07:00  INFO 26608 --- [qtp217257457-36] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-09-17T11:25:47.632+07:00  INFO 26608 --- [qtp217257457-36] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node08p8nygfh3b33ot0jwtuapxec1
2025-09-17T11:25:47.632+07:00  INFO 26608 --- [qtp217257457-40] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01r2btbrj2zlfw42wpbfyz1rrx0
2025-09-17T11:25:47.713+07:00  INFO 26608 --- [qtp217257457-36] n.d.module.session.ClientSessionManager  : Add a client session id = node08p8nygfh3b33ot0jwtuapxec1, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:25:47.735+07:00  INFO 26608 --- [qtp217257457-40] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:25:48.076+07:00  INFO 26608 --- [qtp217257457-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:25:48.086+07:00  INFO 26608 --- [qtp217257457-40] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:26:02.138+07:00  INFO 26608 --- [qtp217257457-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T11:26:02.166+07:00  INFO 26608 --- [qtp217257457-61] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T11:26:02.211+07:00  INFO 26608 --- [qtp217257457-36] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-17T11:26:02.234+07:00  INFO 26608 --- [qtp217257457-61] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-17T11:26:04.245+07:00  INFO 26608 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:26:16.963+07:00  INFO 26608 --- [qtp217257457-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T11:26:16.964+07:00  INFO 26608 --- [qtp217257457-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T11:26:16.975+07:00  INFO 26608 --- [qtp217257457-37] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-17T11:26:16.981+07:00  INFO 26608 --- [qtp217257457-36] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-17T11:26:19.163+07:00  INFO 26608 --- [qtp217257457-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T11:26:19.163+07:00  INFO 26608 --- [qtp217257457-40] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T11:26:19.172+07:00  INFO 26608 --- [qtp217257457-36] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-17T11:26:19.172+07:00  INFO 26608 --- [qtp217257457-40] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-17T11:26:32.298+07:00  INFO 26608 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 0
2025-09-17T11:26:32.312+07:00  INFO 26608 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-17T11:27:06.373+07:00  INFO 26608 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:28:03.477+07:00  INFO 26608 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:28:31.548+07:00  INFO 26608 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 0
2025-09-17T11:28:31.563+07:00  INFO 26608 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T11:29:06.633+07:00  INFO 26608 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:30:02.718+07:00  INFO 26608 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T11:30:02.721+07:00  INFO 26608 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-17T11:30:02.726+07:00  INFO 26608 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 17/09/2025@11:30:02+0700
2025-09-17T11:30:02.737+07:00  INFO 26608 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 17/09/2025@11:30:00+0700 to 17/09/2025@11:45:00+0700
2025-09-17T11:30:02.739+07:00  INFO 26608 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 17/09/2025@11:30:00+0700 to 17/09/2025@11:45:00+0700
2025-09-17T11:30:02.740+07:00  INFO 26608 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:30:19.820+07:00  INFO 26608 --- [qtp217257457-40] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:30:19.829+07:00  INFO 26608 --- [qtp217257457-40] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:30:19.859+07:00  INFO 26608 --- [qtp217257457-60] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:30:19.884+07:00  INFO 26608 --- [qtp217257457-60] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:30:28.226+07:00  INFO 26608 --- [qtp217257457-40] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:30:28.240+07:00  INFO 26608 --- [qtp217257457-40] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:30:28.253+07:00  INFO 26608 --- [qtp217257457-41] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:30:28.275+07:00  INFO 26608 --- [qtp217257457-41] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:30:34.695+07:00  INFO 26608 --- [qtp217257457-65] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:30:34.696+07:00  INFO 26608 --- [qtp217257457-66] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:30:34.700+07:00  INFO 26608 --- [qtp217257457-66] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:30:34.700+07:00  INFO 26608 --- [qtp217257457-65] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:30:35.828+07:00  INFO 26608 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-09-17T11:30:35.838+07:00  INFO 26608 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T11:31:05.887+07:00  INFO 26608 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:31:15.331+07:00  INFO 26608 --- [qtp217257457-40] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:31:15.339+07:00  INFO 26608 --- [qtp217257457-40] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:31:15.372+07:00  INFO 26608 --- [qtp217257457-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:31:15.377+07:00  INFO 26608 --- [qtp217257457-39] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:31:57.392+07:00  INFO 26608 --- [qtp217257457-60] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:31:57.418+07:00  INFO 26608 --- [qtp217257457-61] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:31:57.425+07:00  INFO 26608 --- [qtp217257457-60] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:31:57.427+07:00  INFO 26608 --- [qtp217257457-61] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:32:06.628+07:00  INFO 26608 --- [qtp217257457-40] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:32:06.629+07:00  INFO 26608 --- [qtp217257457-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:32:06.632+07:00  INFO 26608 --- [qtp217257457-40] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:32:06.641+07:00  INFO 26608 --- [qtp217257457-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:32:06.992+07:00  INFO 26608 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:32:35.448+07:00  INFO 26608 --- [qtp217257457-65] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:32:35.456+07:00  INFO 26608 --- [qtp217257457-65] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:32:35.494+07:00  INFO 26608 --- [qtp217257457-37] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:32:35.507+07:00  INFO 26608 --- [qtp217257457-37] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:32:36.056+07:00  INFO 26608 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-17T11:32:36.069+07:00  INFO 26608 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T11:32:52.684+07:00  INFO 26608 --- [qtp217257457-65] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:32:52.685+07:00  INFO 26608 --- [qtp217257457-60] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:32:52.690+07:00  INFO 26608 --- [qtp217257457-60] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:32:52.690+07:00  INFO 26608 --- [qtp217257457-65] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:33:01.515+07:00  INFO 26608 --- [qtp217257457-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T11:33:01.516+07:00  INFO 26608 --- [qtp217257457-66] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T11:33:01.527+07:00  INFO 26608 --- [qtp217257457-36] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-17T11:33:01.527+07:00  INFO 26608 --- [qtp217257457-66] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-17T11:33:05.120+07:00  INFO 26608 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:34:06.219+07:00  INFO 26608 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:34:36.293+07:00  INFO 26608 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 13, expire count 0
2025-09-17T11:34:36.320+07:00  INFO 26608 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-17T11:35:04.370+07:00  INFO 26608 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T11:35:04.372+07:00  INFO 26608 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:35:17.563+07:00  INFO 26608 --- [Scheduler-1230101644-1] n.d.m.session.AppHttpSessionListener     : The session node08p8nygfh3b33ot0jwtuapxec1 is destroyed.
2025-09-17T11:35:20.092+07:00  INFO 26608 --- [qtp217257457-40] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:35:20.093+07:00  INFO 26608 --- [qtp217257457-60] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:35:20.117+07:00  INFO 26608 --- [qtp217257457-60] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:35:20.117+07:00  INFO 26608 --- [qtp217257457-40] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:35:47.212+07:00  INFO 26608 --- [qtp217257457-65] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:35:47.230+07:00  INFO 26608 --- [qtp217257457-65] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:35:47.248+07:00  INFO 26608 --- [qtp217257457-35] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:35:47.257+07:00  INFO 26608 --- [qtp217257457-35] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:35:58.951+07:00  INFO 26608 --- [qtp217257457-41] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:35:58.953+07:00  INFO 26608 --- [qtp217257457-35] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:35:58.971+07:00  INFO 26608 --- [qtp217257457-41] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:35:58.971+07:00  INFO 26608 --- [qtp217257457-35] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:36:06.466+07:00  INFO 26608 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:36:35.550+07:00  INFO 26608 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 3
2025-09-17T11:36:35.555+07:00  INFO 26608 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T11:36:51.262+07:00  INFO 26608 --- [qtp217257457-40] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:36:51.291+07:00  INFO 26608 --- [qtp217257457-40] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:36:51.303+07:00  INFO 26608 --- [qtp217257457-69] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:36:51.325+07:00  INFO 26608 --- [qtp217257457-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:37:03.593+07:00  INFO 26608 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:37:06.299+07:00  INFO 26608 --- [qtp217257457-37] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:37:06.301+07:00  INFO 26608 --- [qtp217257457-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:37:06.315+07:00  INFO 26608 --- [qtp217257457-37] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:37:06.315+07:00  INFO 26608 --- [qtp217257457-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:37:24.306+07:00  INFO 26608 --- [qtp217257457-65] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:37:24.307+07:00  INFO 26608 --- [qtp217257457-61] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:37:24.319+07:00  INFO 26608 --- [qtp217257457-65] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:37:24.319+07:00  INFO 26608 --- [qtp217257457-61] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:37:27.974+07:00  INFO 26608 --- [qtp217257457-40] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T11:37:27.975+07:00  INFO 26608 --- [qtp217257457-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T11:37:28.025+07:00  INFO 26608 --- [qtp217257457-40] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-17T11:37:28.025+07:00  INFO 26608 --- [qtp217257457-37] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-17T11:38:02.363+07:00  INFO 26608 --- [qtp217257457-35] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:38:02.365+07:00  INFO 26608 --- [qtp217257457-66] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:38:02.384+07:00  INFO 26608 --- [qtp217257457-35] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:38:02.384+07:00  INFO 26608 --- [qtp217257457-66] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:38:06.701+07:00  INFO 26608 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:38:13.349+07:00  INFO 26608 --- [qtp217257457-35] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:38:13.356+07:00  INFO 26608 --- [qtp217257457-41] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:38:13.377+07:00  INFO 26608 --- [qtp217257457-35] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:38:13.392+07:00  INFO 26608 --- [qtp217257457-41] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:38:17.801+07:00  INFO 26608 --- [qtp217257457-60] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T11:38:17.802+07:00  INFO 26608 --- [qtp217257457-65] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T11:38:17.814+07:00  INFO 26608 --- [qtp217257457-65] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-17T11:38:17.814+07:00  INFO 26608 --- [qtp217257457-60] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-17T11:38:34.808+07:00  INFO 26608 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 1
2025-09-17T11:38:34.822+07:00  INFO 26608 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-17T11:38:35.413+07:00  INFO 26608 --- [qtp217257457-65] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:38:35.413+07:00  INFO 26608 --- [qtp217257457-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:38:35.431+07:00  INFO 26608 --- [qtp217257457-65] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:38:35.431+07:00  INFO 26608 --- [qtp217257457-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:38:40.513+07:00  INFO 26608 --- [qtp217257457-60] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T11:38:40.513+07:00  INFO 26608 --- [qtp217257457-64] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T11:38:40.527+07:00  INFO 26608 --- [qtp217257457-60] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-17T11:38:40.527+07:00  INFO 26608 --- [qtp217257457-64] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-17T11:38:59.642+07:00  INFO 26608 --- [qtp217257457-40] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:38:59.643+07:00  INFO 26608 --- [qtp217257457-60] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:38:59.653+07:00  INFO 26608 --- [qtp217257457-40] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:38:59.653+07:00  INFO 26608 --- [qtp217257457-60] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:39:02.876+07:00  INFO 26608 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:39:12.633+07:00  INFO 26608 --- [qtp217257457-40] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:39:12.634+07:00  INFO 26608 --- [qtp217257457-41] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:39:12.642+07:00  INFO 26608 --- [qtp217257457-40] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:39:12.642+07:00  INFO 26608 --- [qtp217257457-41] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:39:18.340+07:00  INFO 26608 --- [qtp217257457-60] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T11:39:18.340+07:00  INFO 26608 --- [qtp217257457-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T11:39:18.351+07:00  INFO 26608 --- [qtp217257457-60] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-17T11:39:18.351+07:00  INFO 26608 --- [qtp217257457-36] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-17T11:39:52.966+07:00  INFO 26608 --- [qtp217257457-40] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:39:52.968+07:00  INFO 26608 --- [qtp217257457-37] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:39:52.984+07:00  INFO 26608 --- [qtp217257457-37] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:39:52.985+07:00  INFO 26608 --- [qtp217257457-40] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:39:56.245+07:00  INFO 26608 --- [qtp217257457-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T11:39:56.245+07:00  INFO 26608 --- [qtp217257457-41] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T11:39:56.255+07:00  INFO 26608 --- [qtp217257457-36] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-17T11:39:56.255+07:00  INFO 26608 --- [qtp217257457-41] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-17T11:40:05.976+07:00  INFO 26608 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T11:40:05.978+07:00  INFO 26608 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:40:34.035+07:00  INFO 26608 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 1
2025-09-17T11:40:34.062+07:00  INFO 26608 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T11:40:39.321+07:00  INFO 26608 --- [qtp217257457-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:40:39.355+07:00  INFO 26608 --- [qtp217257457-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:40:39.485+07:00  INFO 26608 --- [qtp217257457-64] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:40:39.509+07:00  INFO 26608 --- [qtp217257457-64] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:41:02.105+07:00  INFO 26608 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:41:09.353+07:00  INFO 26608 --- [qtp217257457-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:41:09.359+07:00  INFO 26608 --- [qtp217257457-69] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:41:09.366+07:00  INFO 26608 --- [qtp217257457-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:41:09.366+07:00  INFO 26608 --- [qtp217257457-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:41:13.844+07:00  INFO 26608 --- [qtp217257457-60] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:41:13.851+07:00  INFO 26608 --- [qtp217257457-60] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:41:13.907+07:00  INFO 26608 --- [qtp217257457-91] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:41:13.912+07:00  INFO 26608 --- [qtp217257457-91] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:41:23.303+07:00  INFO 26608 --- [qtp217257457-40] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T11:41:23.303+07:00  INFO 26608 --- [qtp217257457-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T11:41:23.421+07:00  INFO 26608 --- [qtp217257457-40] c.d.f.s.report.PerformanceReportLogic    : Retrieved 17 records
2025-09-17T11:41:23.421+07:00  INFO 26608 --- [qtp217257457-36] c.d.f.s.report.PerformanceReportLogic    : Retrieved 17 records
2025-09-17T11:41:23.434+07:00  INFO 26608 --- [qtp217257457-60] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T11:41:23.435+07:00  INFO 26608 --- [qtp217257457-64] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T11:41:23.907+07:00  INFO 26608 --- [qtp217257457-64] c.d.f.s.report.PerformanceReportLogic    : Retrieved 21 records
2025-09-17T11:41:23.909+07:00  INFO 26608 --- [qtp217257457-60] c.d.f.s.report.PerformanceReportLogic    : Retrieved 21 records
2025-09-17T11:41:44.284+07:00  INFO 26608 --- [qtp217257457-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:41:44.285+07:00  INFO 26608 --- [qtp217257457-40] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:41:44.296+07:00  INFO 26608 --- [qtp217257457-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:41:44.296+07:00  INFO 26608 --- [qtp217257457-40] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:41:50.072+07:00  INFO 26608 --- [qtp217257457-89] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T11:41:50.072+07:00  INFO 26608 --- [qtp217257457-64] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T11:41:50.115+07:00  INFO 26608 --- [qtp217257457-64] c.d.f.s.report.PerformanceReportLogic    : Retrieved 17 records
2025-09-17T11:41:50.115+07:00  INFO 26608 --- [qtp217257457-89] c.d.f.s.report.PerformanceReportLogic    : Retrieved 17 records
2025-09-17T11:41:50.131+07:00  INFO 26608 --- [qtp217257457-64] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T11:41:50.131+07:00  INFO 26608 --- [qtp217257457-89] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T11:41:50.305+07:00  INFO 26608 --- [qtp217257457-64] c.d.f.s.report.PerformanceReportLogic    : Retrieved 21 records
2025-09-17T11:41:50.307+07:00  INFO 26608 --- [qtp217257457-89] c.d.f.s.report.PerformanceReportLogic    : Retrieved 21 records
2025-09-17T11:42:05.238+07:00  INFO 26608 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:42:20.642+07:00  INFO 26608 --- [qtp217257457-64] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:42:20.649+07:00  INFO 26608 --- [qtp217257457-64] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:42:20.663+07:00  INFO 26608 --- [qtp217257457-89] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:42:20.667+07:00  INFO 26608 --- [qtp217257457-89] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:42:23.687+07:00  INFO 26608 --- [qtp217257457-89] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:42:23.693+07:00  INFO 26608 --- [qtp217257457-89] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:42:23.762+07:00  INFO 26608 --- [qtp217257457-34] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:42:23.767+07:00  INFO 26608 --- [qtp217257457-34] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:42:33.328+07:00  INFO 26608 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 13, expire count 0
2025-09-17T11:42:33.367+07:00  INFO 26608 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-17T11:43:06.449+07:00  INFO 26608 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:44:04.551+07:00  INFO 26608 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:44:32.633+07:00  INFO 26608 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 4
2025-09-17T11:44:32.638+07:00  INFO 26608 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T11:45:06.694+07:00  INFO 26608 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:45:06.698+07:00  INFO 26608 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-17T11:45:06.702+07:00  INFO 26608 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 17/09/2025@11:45:06+0700
2025-09-17T11:45:06.746+07:00  INFO 26608 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 1 messages for session 17/09/2025@11:45:00+0700 to 17/09/2025@12:00:00+0700
2025-09-17T11:45:06.747+07:00  INFO 26608 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 📤 message : MAIL - inquiry-overdue-reminder - scheduled at 17/09/2025@11:49:20+0700
2025-09-17T11:45:06.748+07:00  INFO 26608 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 1 messages for session 17/09/2025@11:45:00+0700 to 17/09/2025@12:00:00+0700
2025-09-17T11:45:06.748+07:00  INFO 26608 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T11:46:03.831+07:00  INFO 26608 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:46:31.896+07:00  INFO 26608 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-17T11:46:31.909+07:00  INFO 26608 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T11:47:06.964+07:00  INFO 26608 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:47:10.212+07:00  INFO 26608 --- [qtp217257457-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:47:10.213+07:00  INFO 26608 --- [qtp217257457-91] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:47:10.241+07:00  INFO 26608 --- [qtp217257457-91] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:47:10.241+07:00  INFO 26608 --- [qtp217257457-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:47:18.220+07:00  INFO 26608 --- [qtp217257457-91] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:47:18.221+07:00  INFO 26608 --- [qtp217257457-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:47:18.233+07:00  INFO 26608 --- [qtp217257457-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:47:18.233+07:00  INFO 26608 --- [qtp217257457-91] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:48:01.204+07:00  INFO 26608 --- [qtp217257457-35] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:48:01.230+07:00  INFO 26608 --- [qtp217257457-35] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:48:01.245+07:00  INFO 26608 --- [qtp217257457-101] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:48:01.250+07:00  INFO 26608 --- [qtp217257457-101] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:48:03.064+07:00  INFO 26608 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:48:33.571+07:00  INFO 26608 --- [qtp217257457-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:48:33.609+07:00  INFO 26608 --- [qtp217257457-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:48:33.634+07:00  INFO 26608 --- [qtp217257457-99] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:48:33.641+07:00  INFO 26608 --- [qtp217257457-99] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:48:36.131+07:00  INFO 26608 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 0
2025-09-17T11:48:36.250+07:00  INFO 26608 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T11:48:40.247+07:00  INFO 26608 --- [qtp217257457-34] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:48:40.248+07:00  INFO 26608 --- [qtp217257457-60] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:48:40.252+07:00  INFO 26608 --- [qtp217257457-34] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:48:40.252+07:00  INFO 26608 --- [qtp217257457-60] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:48:54.604+07:00  INFO 26608 --- [qtp217257457-101] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:48:54.607+07:00  INFO 26608 --- [qtp217257457-99] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:48:54.664+07:00  INFO 26608 --- [qtp217257457-101] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:48:54.670+07:00  INFO 26608 --- [qtp217257457-99] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:49:06.310+07:00  INFO 26608 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:49:13.574+07:00  INFO 26608 --- [qtp217257457-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:49:13.575+07:00  INFO 26608 --- [qtp217257457-91] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:49:13.592+07:00  INFO 26608 --- [qtp217257457-91] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:49:13.592+07:00  INFO 26608 --- [qtp217257457-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:49:30.681+07:00  INFO 26608 --- [qtp217257457-61] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:49:30.685+07:00  INFO 26608 --- [qtp217257457-89] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2btbrj2zlfw42wpbfyz1rrx0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T11:49:30.729+07:00  INFO 26608 --- [qtp217257457-61] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:49:30.731+07:00  INFO 26608 --- [qtp217257457-89] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T11:50:02.425+07:00  INFO 26608 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T11:50:02.427+07:00  INFO 26608 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T11:50:27.838+07:00  INFO 26608 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@38712e9c{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-17T11:50:27.839+07:00  INFO 26608 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-17T11:50:27.839+07:00  INFO 26608 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-17T11:50:27.839+07:00  INFO 26608 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-17T11:50:27.840+07:00  INFO 26608 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-17T11:50:27.840+07:00  INFO 26608 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-17T11:50:27.840+07:00  INFO 26608 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-17T11:50:27.840+07:00  INFO 26608 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-17T11:50:27.840+07:00  INFO 26608 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-17T11:50:27.840+07:00  INFO 26608 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-17T11:50:27.840+07:00  INFO 26608 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-17T11:50:27.840+07:00  INFO 26608 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-17T11:50:27.840+07:00  INFO 26608 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-17T11:50:27.840+07:00  INFO 26608 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-17T11:50:27.840+07:00  INFO 26608 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-17T11:50:27.841+07:00  INFO 26608 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-17T11:50:27.888+07:00  INFO 26608 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T11:50:27.978+07:00  INFO 26608 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-17T11:50:27.983+07:00  INFO 26608 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-17T11:50:28.011+07:00  INFO 26608 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-17T11:50:28.026+07:00  INFO 26608 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-17T11:50:28.051+07:00  INFO 26608 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-17T11:50:28.053+07:00  INFO 26608 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-17T11:50:28.055+07:00  INFO 26608 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-17T11:50:28.055+07:00  INFO 26608 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-17T11:50:28.056+07:00  INFO 26608 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-17T11:50:28.056+07:00  INFO 26608 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-17T11:50:28.056+07:00  INFO 26608 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-17T11:50:28.056+07:00  INFO 26608 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-17T11:50:28.056+07:00  INFO 26608 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-17T11:50:28.056+07:00  INFO 26608 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-17T11:50:28.056+07:00  INFO 26608 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-17T11:50:28.063+07:00  INFO 26608 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@30a1b9b0{STOPPING}[12.0.15,sto=0]
2025-09-17T11:50:28.070+07:00  INFO 26608 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-17T11:50:28.072+07:00  INFO 26608 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@3566d527{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.12712423731094366303/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@ae5eeee{STOPPED}}
