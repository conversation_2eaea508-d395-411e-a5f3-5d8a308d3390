2025-09-16T16:48:12.039+07:00  INFO 26380 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 26380 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-16T16:48:12.050+07:00  INFO 26380 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-16T16:48:12.903+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T16:48:12.969+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 62 ms. Found 22 JPA repository interfaces.
2025-09-16T16:48:12.977+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T16:48:12.979+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-16T16:48:12.979+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T16:48:12.986+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 10 JPA repository interfaces.
2025-09-16T16:48:12.987+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T16:48:13.029+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 41 ms. Found 3 JPA repository interfaces.
2025-09-16T16:48:13.038+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T16:48:13.043+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-09-16T16:48:13.051+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T16:48:13.053+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-16T16:48:13.054+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T16:48:13.057+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-16T16:48:13.060+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T16:48:13.064+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-09-16T16:48:13.067+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T16:48:13.070+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-16T16:48:13.070+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T16:48:13.070+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-16T16:48:13.071+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T16:48:13.076+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 10 JPA repository interfaces.
2025-09-16T16:48:13.081+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T16:48:13.083+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-16T16:48:13.086+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T16:48:13.090+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-16T16:48:13.090+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T16:48:13.097+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-16T16:48:13.097+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T16:48:13.099+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-16T16:48:13.100+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T16:48:13.100+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-16T16:48:13.100+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T16:48:13.101+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-16T16:48:13.101+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T16:48:13.105+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-16T16:48:13.105+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T16:48:13.106+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-16T16:48:13.106+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T16:48:13.106+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-16T16:48:13.106+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T16:48:13.116+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 19 JPA repository interfaces.
2025-09-16T16:48:13.126+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T16:48:13.132+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-09-16T16:48:13.132+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T16:48:13.135+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-09-16T16:48:13.135+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T16:48:13.139+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-09-16T16:48:13.139+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T16:48:13.144+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 9 JPA repository interfaces.
2025-09-16T16:48:13.144+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T16:48:13.148+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-16T16:48:13.149+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T16:48:13.157+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-09-16T16:48:13.157+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T16:48:13.166+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 14 JPA repository interfaces.
2025-09-16T16:48:13.167+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T16:48:13.180+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 13 ms. Found 24 JPA repository interfaces.
2025-09-16T16:48:13.181+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T16:48:13.182+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-16T16:48:13.187+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T16:48:13.188+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-16T16:48:13.188+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T16:48:13.195+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-16T16:48:13.197+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T16:48:13.232+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 35 ms. Found 66 JPA repository interfaces.
2025-09-16T16:48:13.232+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T16:48:13.234+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-16T16:48:13.238+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16T16:48:13.241+07:00  INFO 26380 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-16T16:48:13.513+07:00  INFO 26380 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-16T16:48:13.517+07:00  INFO 26380 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-16T16:48:13.806+07:00  WARN 26380 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-16T16:48:14.005+07:00  INFO 26380 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-16T16:48:14.007+07:00  INFO 26380 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-16T16:48:14.021+07:00  INFO 26380 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-16T16:48:14.021+07:00  INFO 26380 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1774 ms
2025-09-16T16:48:14.083+07:00  WARN 26380 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-16T16:48:14.083+07:00  INFO 26380 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-16T16:48:14.185+07:00  INFO 26380 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@45b7b254
2025-09-16T16:48:14.186+07:00  INFO 26380 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-16T16:48:14.190+07:00  WARN 26380 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-16T16:48:14.191+07:00  INFO 26380 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-16T16:48:14.195+07:00  INFO 26380 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@27acd9a7
2025-09-16T16:48:14.195+07:00  INFO 26380 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-16T16:48:14.196+07:00  WARN 26380 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-16T16:48:14.196+07:00  INFO 26380 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-16T16:48:14.204+07:00  INFO 26380 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@61236349
2025-09-16T16:48:14.204+07:00  INFO 26380 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-16T16:48:14.204+07:00  WARN 26380 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-16T16:48:14.204+07:00  INFO 26380 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-16T16:48:14.210+07:00  INFO 26380 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@591bff1d
2025-09-16T16:48:14.210+07:00  INFO 26380 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-16T16:48:14.210+07:00  WARN 26380 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-16T16:48:14.210+07:00  INFO 26380 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-16T16:48:14.217+07:00  INFO 26380 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@1e0383d3
2025-09-16T16:48:14.217+07:00  INFO 26380 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-16T16:48:14.217+07:00  INFO 26380 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-16T16:48:14.261+07:00  INFO 26380 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-16T16:48:14.263+07:00  INFO 26380 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@61bbe16d{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.5774721999463202068/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@5fc3bf09{STARTED}}
2025-09-16T16:48:14.263+07:00  INFO 26380 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@61bbe16d{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.5774721999463202068/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@5fc3bf09{STARTED}}
2025-09-16T16:48:14.264+07:00  INFO 26380 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@2c64e2d0{STARTING}[12.0.15,sto=0] @2859ms
2025-09-16T16:48:14.362+07:00  INFO 26380 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-16T16:48:14.388+07:00  INFO 26380 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-16T16:48:14.403+07:00  INFO 26380 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-16T16:48:14.528+07:00  INFO 26380 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-16T16:48:14.565+07:00  WARN 26380 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-16T16:48:15.174+07:00  INFO 26380 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-16T16:48:15.185+07:00  INFO 26380 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@5ee3ca50] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-16T16:48:15.327+07:00  INFO 26380 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-16T16:48:15.541+07:00  INFO 26380 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.graphapi", "net.datatp.module.kpi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "cloud.datatp.tms", "net.datatp.module.groovy", "net.datatp.module.company.tmpl", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.chatbot", "cloud.datatp.module.odoo", "net.datatp.module.core.print", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.zalo", "net.datatp.module.accounting" ]
2025-09-16T16:48:15.543+07:00  INFO 26380 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-16T16:48:15.549+07:00  INFO 26380 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-16T16:48:15.550+07:00  INFO 26380 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-16T16:48:15.576+07:00  INFO 26380 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-16T16:48:15.589+07:00  WARN 26380 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-16T16:48:17.659+07:00  INFO 26380 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-16T16:48:17.659+07:00  INFO 26380 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@4d9f21e6] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-16T16:48:17.833+07:00  WARN 26380 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-16T16:48:17.834+07:00  WARN 26380 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-16T16:48:17.843+07:00  WARN 26380 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-16T16:48:17.843+07:00  WARN 26380 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-16T16:48:17.857+07:00  WARN 26380 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-16T16:48:17.857+07:00  WARN 26380 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-16T16:48:18.332+07:00  INFO 26380 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-16T16:48:18.338+07:00  INFO 26380 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-16T16:48:18.340+07:00  INFO 26380 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-16T16:48:18.359+07:00  INFO 26380 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-16T16:48:18.362+07:00  WARN 26380 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-16T16:48:18.893+07:00  INFO 26380 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-16T16:48:18.894+07:00  INFO 26380 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@59c4d212] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-16T16:48:19.013+07:00  WARN 26380 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-16T16:48:19.013+07:00  WARN 26380 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-16T16:48:19.330+07:00  INFO 26380 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-16T16:48:19.362+07:00  INFO 26380 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-16T16:48:19.366+07:00  INFO 26380 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-16T16:48:19.366+07:00  INFO 26380 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-16T16:48:19.372+07:00  WARN 26380 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-16T16:48:19.500+07:00  INFO 26380 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-16T16:48:19.967+07:00  INFO 26380 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-16T16:48:19.970+07:00  INFO 26380 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-16T16:48:20.005+07:00  INFO 26380 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-16T16:48:20.051+07:00  INFO 26380 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-16T16:48:20.103+07:00  INFO 26380 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-16T16:48:20.131+07:00  INFO 26380 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-16T16:48:20.154+07:00  INFO 26380 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 83058492ms : this is harmless.
2025-09-16T16:48:20.162+07:00  INFO 26380 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-16T16:48:20.165+07:00  INFO 26380 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-16T16:48:20.180+07:00  INFO 26380 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 374564448ms : this is harmless.
2025-09-16T16:48:20.181+07:00  INFO 26380 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-16T16:48:20.194+07:00  INFO 26380 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-16T16:48:20.195+07:00  INFO 26380 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-16T16:48:22.189+07:00  INFO 26380 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-16T16:48:22.189+07:00  INFO 26380 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-16T16:48:22.189+07:00  WARN 26380 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-16T16:48:22.487+07:00  INFO 26380 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 1 messages for session 16/09/2025@16:45:00+0700 to 16/09/2025@17:00:00+0700
2025-09-16T16:48:22.487+07:00  INFO 26380 --- [main] c.d.f.core.message.MessageQueueManager   : 📤 message : ZALO - inquiry-overdue-reminder - scheduled at 16/09/2025@16:46:56+0700
2025-09-16T16:48:22.487+07:00  INFO 26380 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 1 messages for session 16/09/2025@16:45:00+0700 to 16/09/2025@17:00:00+0700
2025-09-16T16:48:23.018+07:00  INFO 26380 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-16T16:48:23.018+07:00  INFO 26380 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-16T16:48:23.019+07:00  WARN 26380 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-16T16:48:23.299+07:00  INFO 26380 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-16T16:48:23.299+07:00  INFO 26380 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-16T16:48:23.299+07:00  INFO 26380 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-16T16:48:23.299+07:00  INFO 26380 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-16T16:48:23.299+07:00  INFO 26380 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-16T16:48:25.018+07:00  WARN 26380 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: fa5a6a5a-e849-43c8-8084-751365a4c690

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-16T16:48:25.022+07:00  INFO 26380 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-16T16:48:25.306+07:00  INFO 26380 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-16T16:48:25.306+07:00  INFO 26380 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-16T16:48:25.306+07:00  INFO 26380 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-16T16:48:25.306+07:00  INFO 26380 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-16T16:48:25.306+07:00  INFO 26380 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-16T16:48:25.306+07:00  INFO 26380 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-16T16:48:25.306+07:00  INFO 26380 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-16T16:48:25.306+07:00  INFO 26380 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-16T16:48:25.306+07:00  INFO 26380 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-16T16:48:25.306+07:00  INFO 26380 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-16T16:48:25.306+07:00  INFO 26380 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-16T16:48:25.306+07:00  INFO 26380 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-16T16:48:25.307+07:00  INFO 26380 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-16T16:48:25.307+07:00  INFO 26380 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-16T16:48:25.307+07:00  INFO 26380 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-16T16:48:25.443+07:00  INFO 26380 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-16T16:48:25.444+07:00  INFO 26380 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-16T16:48:25.445+07:00  INFO 26380 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-09-16T16:48:25.453+07:00  INFO 26380 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@5dcfec61{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-16T16:48:25.454+07:00  INFO 26380 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-16T16:48:25.455+07:00  INFO 26380 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-16T16:48:25.488+07:00  INFO 26380 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-16T16:48:25.488+07:00  INFO 26380 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-16T16:48:25.494+07:00  INFO 26380 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 13.894 seconds (process running for 14.089)
2025-09-16T16:49:06.407+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T16:49:12.202+07:00  INFO 26380 --- [qtp1767958438-36] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-09-16T16:49:28.482+07:00  INFO 26380 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-16T16:49:28.501+07:00  INFO 26380 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-16T16:50:03.559+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T16:50:03.560+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-16T16:51:06.656+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T16:51:27.704+07:00  INFO 26380 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-16T16:51:27.710+07:00  INFO 26380 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-16T16:52:02.769+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T16:53:05.893+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T16:53:31.957+07:00  INFO 26380 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-16T16:53:32.011+07:00  INFO 26380 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-16T16:54:02.077+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T16:55:05.182+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T16:55:05.184+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-16T16:55:32.231+07:00  INFO 26380 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-16T16:55:32.239+07:00  INFO 26380 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-16T16:56:06.296+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T16:57:04.400+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T16:57:13.833+07:00  INFO 26380 --- [qtp1767958438-39] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node019ow3922q7d8x1kmr4c3ritpkt0
2025-09-16T16:57:13.833+07:00  INFO 26380 --- [qtp1767958438-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0b081w43aw4kd1f3iffs6uiqji1
2025-09-16T16:57:13.975+07:00  INFO 26380 --- [qtp1767958438-39] n.d.module.session.ClientSessionManager  : Add a client session id = node019ow3922q7d8x1kmr4c3ritpkt0, token = 70de5477b5c484b3f681c1a7b13128d9
2025-09-16T16:57:13.986+07:00  INFO 26380 --- [qtp1767958438-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0b081w43aw4kd1f3iffs6uiqji1, token = 70de5477b5c484b3f681c1a7b13128d9
2025-09-16T16:57:14.399+07:00  INFO 26380 --- [qtp1767958438-39] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-16T16:57:14.403+07:00  INFO 26380 --- [qtp1767958438-35] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-16T16:57:32.473+07:00  INFO 26380 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 0
2025-09-16T16:57:32.497+07:00  INFO 26380 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-16T16:58:06.553+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T16:58:29.958+07:00  INFO 26380 --- [qtp1767958438-64] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-16T16:58:29.958+07:00  INFO 26380 --- [qtp1767958438-34] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-16T16:58:30.220+07:00  INFO 26380 --- [qtp1767958438-64] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-16T16:58:30.220+07:00  INFO 26380 --- [qtp1767958438-34] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-16T16:58:33.016+07:00  INFO 26380 --- [qtp1767958438-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-16T16:58:33.016+07:00  INFO 26380 --- [qtp1767958438-66] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-16T16:58:33.931+07:00  INFO 26380 --- [qtp1767958438-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-16T16:58:33.963+07:00  INFO 26380 --- [qtp1767958438-66] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-16T16:58:35.053+07:00  INFO 26380 --- [qtp1767958438-64] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-16T16:58:35.053+07:00  INFO 26380 --- [qtp1767958438-60] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-16T16:58:37.373+07:00  INFO 26380 --- [qtp1767958438-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-16T16:58:37.373+07:00  INFO 26380 --- [qtp1767958438-41] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-16T16:58:39.036+07:00  INFO 26380 --- [qtp1767958438-66] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-16T16:58:39.036+07:00  INFO 26380 --- [qtp1767958438-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-16T16:58:39.324+07:00  INFO 26380 --- [qtp1767958438-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-16T16:58:39.365+07:00  INFO 26380 --- [qtp1767958438-66] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-16T16:58:39.445+07:00  INFO 26380 --- [qtp1767958438-39] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-16T16:58:39.445+07:00  INFO 26380 --- [qtp1767958438-34] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-16T16:59:03.665+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T16:59:31.745+07:00  INFO 26380 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 1
2025-09-16T16:59:31.759+07:00  INFO 26380 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-16T17:00:06.820+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T17:00:06.822+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-16T17:00:06.823+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-09-16T17:00:06.824+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-16T17:00:06.827+07:00  INFO 26380 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 16/09/2025@17:00:06+0700
2025-09-16T17:00:06.840+07:00  INFO 26380 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 1 messages for session 16/09/2025@17:00:00+0700 to 16/09/2025@17:15:00+0700
2025-09-16T17:00:06.840+07:00  INFO 26380 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 📤 message : ZALO - inquiry-overdue-reminder - scheduled at 16/09/2025@17:01:12+0700
2025-09-16T17:00:06.841+07:00  INFO 26380 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 1 messages for session 16/09/2025@17:00:00+0700 to 16/09/2025@17:15:00+0700
2025-09-16T17:01:02.956+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T17:01:31.039+07:00  INFO 26380 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-16T17:01:31.047+07:00  INFO 26380 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-16T17:02:06.106+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T17:03:02.216+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T17:03:30.275+07:00  INFO 26380 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-16T17:03:30.281+07:00  INFO 26380 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-16T17:04:05.344+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T17:05:06.451+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T17:05:06.453+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-16T17:05:29.517+07:00  INFO 26380 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-16T17:05:29.536+07:00  INFO 26380 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-16T17:05:57.445+07:00  INFO 26380 --- [qtp1767958438-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-16T17:05:57.445+07:00  INFO 26380 --- [qtp1767958438-66] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-16T17:05:57.471+07:00  INFO 26380 --- [qtp1767958438-37] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-16T17:05:57.471+07:00  INFO 26380 --- [qtp1767958438-66] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-16T17:06:04.730+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T17:06:08.122+07:00  INFO 26380 --- [qtp1767958438-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-16T17:06:08.133+07:00  INFO 26380 --- [qtp1767958438-89] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-16T17:06:08.221+07:00  INFO 26380 --- [qtp1767958438-89] c.d.f.s.report.PerformanceReportLogic    : Retrieved 17 records
2025-09-16T17:06:08.221+07:00  INFO 26380 --- [qtp1767958438-37] c.d.f.s.report.PerformanceReportLogic    : Retrieved 17 records
2025-09-16T17:06:08.231+07:00  INFO 26380 --- [qtp1767958438-61] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-16T17:06:08.231+07:00  INFO 26380 --- [qtp1767958438-89] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-16T17:06:08.609+07:00  INFO 26380 --- [qtp1767958438-61] c.d.f.s.report.PerformanceReportLogic    : Retrieved 21 records
2025-09-16T17:06:08.609+07:00  INFO 26380 --- [qtp1767958438-89] c.d.f.s.report.PerformanceReportLogic    : Retrieved 21 records
2025-09-16T17:07:06.833+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T17:07:28.895+07:00  INFO 26380 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 14, expire count 2
2025-09-16T17:07:28.924+07:00  INFO 26380 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-16T17:08:03.994+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T17:08:14.326+07:00  INFO 26380 --- [Scheduler-1889288666-1] n.d.m.session.AppHttpSessionListener     : The session node019ow3922q7d8x1kmr4c3ritpkt0 is destroyed.
2025-09-16T17:08:15.489+07:00  INFO 26380 --- [qtp1767958438-61] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-09-16T17:09:06.089+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T17:09:16.490+07:00  INFO 26380 --- [qtp1767958438-89] n.d.module.session.ClientSessionManager  : Add a client session id = node0b081w43aw4kd1f3iffs6uiqji1, token = 70de5477b5c484b3f681c1a7b13128d9
2025-09-16T17:09:16.490+07:00  INFO 26380 --- [qtp1767958438-61] n.d.module.session.ClientSessionManager  : Add a client session id = node0b081w43aw4kd1f3iffs6uiqji1, token = 70de5477b5c484b3f681c1a7b13128d9
2025-09-16T17:09:16.530+07:00  INFO 26380 --- [qtp1767958438-61] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-16T17:09:16.530+07:00  INFO 26380 --- [qtp1767958438-89] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-16T17:09:28.166+07:00  INFO 26380 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 4
2025-09-16T17:09:28.176+07:00  INFO 26380 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-16T17:10:03.203+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T17:10:03.207+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-16T17:11:06.288+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T17:11:32.343+07:00  INFO 26380 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 2
2025-09-16T17:11:32.369+07:00  INFO 26380 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-16T17:12:02.425+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T17:13:05.539+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T17:13:31.587+07:00  INFO 26380 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-16T17:13:31.603+07:00  INFO 26380 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-16T17:14:06.669+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T17:14:38.974+07:00  INFO 26380 --- [qtp1767958438-64] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-16T17:14:38.974+07:00  INFO 26380 --- [qtp1767958438-66] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-16T17:14:39.851+07:00  INFO 26380 --- [qtp1767958438-65] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-16T17:14:39.851+07:00  INFO 26380 --- [qtp1767958438-93] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-16T17:14:39.958+07:00  INFO 26380 --- [qtp1767958438-66] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-16T17:14:40.006+07:00  INFO 26380 --- [qtp1767958438-64] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-16T17:15:04.783+07:00  INFO 26380 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 16/09/2025@17:15:04+0700
2025-09-16T17:15:04.799+07:00  INFO 26380 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 1 messages for session 16/09/2025@17:15:00+0700 to 16/09/2025@17:30:00+0700
2025-09-16T17:15:04.800+07:00  INFO 26380 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 📤 message : ZALO - inquiry-overdue-reminder - scheduled at 16/09/2025@17:21:11+0700
2025-09-16T17:15:04.800+07:00  INFO 26380 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 1 messages for session 16/09/2025@17:15:00+0700 to 16/09/2025@17:30:00+0700
2025-09-16T17:15:04.801+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-16T17:15:04.802+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-16T17:15:04.802+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T17:15:31.863+07:00  INFO 26380 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 0
2025-09-16T17:15:31.877+07:00  INFO 26380 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-16T17:16:06.936+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T17:17:04.027+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T17:17:32.090+07:00  INFO 26380 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 16
2025-09-16T17:17:32.112+07:00  INFO 26380 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-16T17:18:06.160+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T17:19:03.248+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T17:19:31.306+07:00  INFO 26380 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 4
2025-09-16T17:19:31.314+07:00  INFO 26380 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-16T17:20:06.382+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T17:20:06.383+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-16T17:21:02.470+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T17:21:30.529+07:00  INFO 26380 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-16T17:21:30.539+07:00  INFO 26380 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-16T17:22:05.598+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T17:23:06.714+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T17:23:29.760+07:00  INFO 26380 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-16T17:23:29.764+07:00  INFO 26380 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-16T17:24:04.828+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T17:25:06.930+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T17:25:06.933+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-16T17:25:28.981+07:00  INFO 26380 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 7
2025-09-16T17:25:28.997+07:00  INFO 26380 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-16T17:26:04.054+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T17:27:06.170+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T17:27:28.218+07:00  INFO 26380 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-16T17:27:28.227+07:00  INFO 26380 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-16T17:28:03.280+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T17:28:26.752+07:00  INFO 26380 --- [qtp1767958438-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0b081w43aw4kd1f3iffs6uiqji1, token = 70de5477b5c484b3f681c1a7b13128d9
2025-09-16T17:28:26.753+07:00  INFO 26380 --- [qtp1767958438-41] n.d.module.session.ClientSessionManager  : Add a client session id = node0b081w43aw4kd1f3iffs6uiqji1, token = 70de5477b5c484b3f681c1a7b13128d9
2025-09-16T17:28:26.840+07:00  INFO 26380 --- [qtp1767958438-60] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-16T17:28:26.851+07:00  INFO 26380 --- [qtp1767958438-41] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-16T17:28:38.058+07:00  INFO 26380 --- [qtp1767958438-92] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-16T17:28:38.092+07:00  INFO 26380 --- [qtp1767958438-193] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-16T17:28:38.149+07:00  INFO 26380 --- [qtp1767958438-92] c.d.f.s.report.PerformanceReportLogic    : Retrieved 17 records
2025-09-16T17:28:38.149+07:00  INFO 26380 --- [qtp1767958438-193] c.d.f.s.report.PerformanceReportLogic    : Retrieved 17 records
2025-09-16T17:28:38.161+07:00  INFO 26380 --- [qtp1767958438-41] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-16T17:28:38.161+07:00  INFO 26380 --- [qtp1767958438-189] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-16T17:28:38.303+07:00  INFO 26380 --- [qtp1767958438-41] c.d.f.s.report.PerformanceReportLogic    : Retrieved 21 records
2025-09-16T17:28:38.304+07:00  INFO 26380 --- [qtp1767958438-189] c.d.f.s.report.PerformanceReportLogic    : Retrieved 21 records
2025-09-16T17:29:06.386+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T17:29:32.454+07:00  INFO 26380 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 13, expire count 0
2025-09-16T17:29:32.475+07:00  INFO 26380 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-16T17:29:38.090+07:00  INFO 26380 --- [qtp1767958438-66] n.d.module.session.ClientSessionManager  : Add a client session id = node0b081w43aw4kd1f3iffs6uiqji1, token = 70de5477b5c484b3f681c1a7b13128d9
2025-09-16T17:29:38.091+07:00  INFO 26380 --- [qtp1767958438-193] n.d.module.session.ClientSessionManager  : Add a client session id = node0b081w43aw4kd1f3iffs6uiqji1, token = 70de5477b5c484b3f681c1a7b13128d9
2025-09-16T17:29:38.100+07:00  INFO 26380 --- [qtp1767958438-66] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-16T17:29:38.100+07:00  INFO 26380 --- [qtp1767958438-193] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-16T17:29:49.164+07:00  INFO 26380 --- [qtp1767958438-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0b081w43aw4kd1f3iffs6uiqji1, token = 70de5477b5c484b3f681c1a7b13128d9
2025-09-16T17:29:49.165+07:00  INFO 26380 --- [qtp1767958438-41] n.d.module.session.ClientSessionManager  : Add a client session id = node0b081w43aw4kd1f3iffs6uiqji1, token = 70de5477b5c484b3f681c1a7b13128d9
2025-09-16T17:29:49.174+07:00  INFO 26380 --- [qtp1767958438-41] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-16T17:29:49.174+07:00  INFO 26380 --- [qtp1767958438-60] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-16T17:29:52.188+07:00  INFO 26380 --- [qtp1767958438-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0b081w43aw4kd1f3iffs6uiqji1, token = 70de5477b5c484b3f681c1a7b13128d9
2025-09-16T17:29:52.199+07:00  INFO 26380 --- [qtp1767958438-60] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-16T17:29:52.215+07:00  INFO 26380 --- [qtp1767958438-186] n.d.module.session.ClientSessionManager  : Add a client session id = node0b081w43aw4kd1f3iffs6uiqji1, token = 70de5477b5c484b3f681c1a7b13128d9
2025-09-16T17:29:52.221+07:00  INFO 26380 --- [qtp1767958438-186] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-16T17:30:02.534+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-16T17:30:02.536+07:00  INFO 26380 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 16/09/2025@17:30:02+0700
2025-09-16T17:30:02.546+07:00  INFO 26380 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 5 messages for session 16/09/2025@17:30:00+0700 to 16/09/2025@17:45:00+0700
2025-09-16T17:30:02.546+07:00  INFO 26380 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 📤 message : ZALO - inquiry-overdue-reminder - scheduled at 16/09/2025@17:34:13+0700
2025-09-16T17:30:02.546+07:00  INFO 26380 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 📤 message : ZALO - inquiry-overdue-reminder - scheduled at 16/09/2025@17:34:40+0700
2025-09-16T17:30:02.546+07:00  INFO 26380 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 📤 message : ZALO - inquiry-overdue-reminder - scheduled at 16/09/2025@17:34:48+0700
2025-09-16T17:30:02.547+07:00  INFO 26380 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 📤 message : ZALO - inquiry-overdue-reminder - scheduled at 16/09/2025@17:42:58+0700
2025-09-16T17:30:02.547+07:00  INFO 26380 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 📤 message : ZALO - inquiry-overdue-reminder - scheduled at 16/09/2025@17:43:39+0700
2025-09-16T17:30:02.547+07:00  INFO 26380 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 5 messages for session 16/09/2025@17:30:00+0700 to 16/09/2025@17:45:00+0700
2025-09-16T17:30:02.548+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-16T17:30:02.548+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T17:30:20.509+07:00  INFO 26380 --- [qtp1767958438-41] n.d.module.session.ClientSessionManager  : Add a client session id = node0b081w43aw4kd1f3iffs6uiqji1, token = 70de5477b5c484b3f681c1a7b13128d9
2025-09-16T17:30:20.525+07:00  INFO 26380 --- [qtp1767958438-41] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-16T17:30:20.542+07:00  INFO 26380 --- [qtp1767958438-189] n.d.module.session.ClientSessionManager  : Add a client session id = node0b081w43aw4kd1f3iffs6uiqji1, token = 70de5477b5c484b3f681c1a7b13128d9
2025-09-16T17:30:20.552+07:00  INFO 26380 --- [qtp1767958438-189] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-16T17:30:25.135+07:00  INFO 26380 --- [qtp1767958438-41] n.d.module.session.ClientSessionManager  : Add a client session id = node0b081w43aw4kd1f3iffs6uiqji1, token = 70de5477b5c484b3f681c1a7b13128d9
2025-09-16T17:30:25.150+07:00  INFO 26380 --- [qtp1767958438-41] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-16T17:30:25.247+07:00  INFO 26380 --- [qtp1767958438-92] n.d.module.session.ClientSessionManager  : Add a client session id = node0b081w43aw4kd1f3iffs6uiqji1, token = 70de5477b5c484b3f681c1a7b13128d9
2025-09-16T17:30:25.265+07:00  INFO 26380 --- [qtp1767958438-92] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-16T17:30:56.317+07:00  INFO 26380 --- [qtp1767958438-186] n.d.module.session.ClientSessionManager  : Add a client session id = node0b081w43aw4kd1f3iffs6uiqji1, token = 70de5477b5c484b3f681c1a7b13128d9
2025-09-16T17:30:56.319+07:00  INFO 26380 --- [qtp1767958438-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0b081w43aw4kd1f3iffs6uiqji1, token = 70de5477b5c484b3f681c1a7b13128d9
2025-09-16T17:30:56.333+07:00  INFO 26380 --- [qtp1767958438-60] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-16T17:30:56.333+07:00  INFO 26380 --- [qtp1767958438-186] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-16T17:31:04.434+07:00  INFO 26380 --- [qtp1767958438-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0b081w43aw4kd1f3iffs6uiqji1, token = 70de5477b5c484b3f681c1a7b13128d9
2025-09-16T17:31:04.435+07:00  INFO 26380 --- [qtp1767958438-41] n.d.module.session.ClientSessionManager  : Add a client session id = node0b081w43aw4kd1f3iffs6uiqji1, token = 70de5477b5c484b3f681c1a7b13128d9
2025-09-16T17:31:04.446+07:00  INFO 26380 --- [qtp1767958438-60] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-16T17:31:04.446+07:00  INFO 26380 --- [qtp1767958438-41] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-16T17:31:05.654+07:00  INFO 26380 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-16T17:31:11.406+07:00  INFO 26380 --- [qtp1767958438-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0b081w43aw4kd1f3iffs6uiqji1, token = 70de5477b5c484b3f681c1a7b13128d9
2025-09-16T17:31:11.412+07:00  INFO 26380 --- [qtp1767958438-193] n.d.module.session.ClientSessionManager  : Add a client session id = node0b081w43aw4kd1f3iffs6uiqji1, token = 70de5477b5c484b3f681c1a7b13128d9
2025-09-16T17:31:11.422+07:00  INFO 26380 --- [qtp1767958438-60] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-16T17:31:11.422+07:00  INFO 26380 --- [qtp1767958438-193] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-16T17:31:31.729+07:00  INFO 26380 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-16T17:31:31.745+07:00  INFO 26380 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-16T17:31:40.862+07:00  INFO 26380 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@5dcfec61{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-16T17:31:40.863+07:00  INFO 26380 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-16T17:31:40.863+07:00  INFO 26380 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-16T17:31:40.863+07:00  INFO 26380 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-16T17:31:40.864+07:00  INFO 26380 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-16T17:31:40.864+07:00  INFO 26380 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-16T17:31:40.864+07:00  INFO 26380 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-16T17:31:40.864+07:00  INFO 26380 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-16T17:31:40.864+07:00  INFO 26380 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-16T17:31:40.864+07:00  INFO 26380 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-16T17:31:40.864+07:00  INFO 26380 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-16T17:31:40.864+07:00  INFO 26380 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-16T17:31:40.864+07:00  INFO 26380 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-16T17:31:40.864+07:00  INFO 26380 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-16T17:31:40.864+07:00  INFO 26380 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-16T17:31:40.864+07:00  INFO 26380 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-16T17:31:40.879+07:00  INFO 26380 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-16T17:31:40.936+07:00  INFO 26380 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-16T17:31:40.942+07:00  INFO 26380 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-16T17:31:40.966+07:00  INFO 26380 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-16T17:31:40.968+07:00  INFO 26380 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-16T17:31:40.970+07:00  INFO 26380 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-16T17:31:40.970+07:00  INFO 26380 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-16T17:31:40.971+07:00  INFO 26380 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-16T17:31:40.971+07:00  INFO 26380 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-16T17:31:40.971+07:00  INFO 26380 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-16T17:31:40.971+07:00  INFO 26380 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-16T17:31:40.972+07:00  INFO 26380 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-16T17:31:40.972+07:00  INFO 26380 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-16T17:31:40.972+07:00  INFO 26380 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-16T17:31:40.972+07:00  INFO 26380 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-16T17:31:40.972+07:00  INFO 26380 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-16T17:31:40.976+07:00  INFO 26380 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@2c64e2d0{STOPPING}[12.0.15,sto=0]
2025-09-16T17:31:40.979+07:00  INFO 26380 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-16T17:31:40.981+07:00  INFO 26380 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@61bbe16d{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.5774721999463202068/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@5fc3bf09{STOPPED}}
