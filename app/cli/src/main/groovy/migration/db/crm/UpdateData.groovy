package migration.db.crm

import lib.data.DBMigrationRunnable
import lib.data.DBMigrationRunnableSet
import lib.data.RunnableReporter
import net.datatp.cli.ShellApplicationContext
import net.datatp.module.data.db.util.DBConnectionUtil
import net.datatp.module.data.db.util.DBUtil

import javax.sql.DataSource
import java.sql.Connection

public class UpdateCRMDataSet extends DBMigrationRunnableSet {
    public UpdateCRMDataSet() {
        super("""Drop CRM Tables In DataTP Database""");

    String label = """Drop CRM Tables In DataTP Database""";
    DBMigrationRunnable alterTables = new DBMigrationRunnable(label) {
        @Override
        public void run(RunnableReporter reporter, DBConnectionUtil connUtil) {
          String query = """
            ALTER TABLE lgc_forwarder_crm_partner ADD status varchar(255) NULL;
            UPDATE lgc_forwarder_crm_partner SET status = 'NEW';
          """;
          connUtil.execute(query);
        }
    };
    addRunnable(alterTables);
  }
}

ShellApplicationContext shellContext = (ShellApplicationContext) SHELL_CONTEXT;

DataSource crmDs = DBUtil.createPostgresDs('datatp-crm', 'datatp-crm', '**********************************************');
Connection conn = crmDs.getConnection();
DBConnectionUtil connUtil = new DBConnectionUtil(conn);

RunnableReporter reporter = new RunnableReporter("dbmigration", "latest")

UpdateCRMDataSet migration = new UpdateCRMDataSet();
migration.run(reporter, connUtil);

connUtil.close();
return "DONE!!!"