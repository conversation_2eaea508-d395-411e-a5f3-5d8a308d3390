import React, { Component, ReactElement, ChangeEvent, KeyboardEvent } from 'react';
import * as FeatherIcon from 'react-feather'
import moment from 'moment';
import DateTime from 'react-datetime';

import IMask, { FactoryArg, InputMask } from 'imask';

import { formater } from 'util/text';
import { Validator } from '../../util/validator';

import * as bs from '../core';
import { ELEProps, mergeCssClass, Button, notificationShow } from '../core';
import 'react-datetime/css/react-datetime.css'
import "./stylesheet.scss";

function isNormalInteger(value: any): boolean {
  var x = parseFloat(value);
  return !isNaN(value) && (x | 0) === x;
}

function isDecimal(str: string): boolean {
  return str.match(/^[-+]?[0-9]+(\.[0-9]+)?$/) != null;
}

export class ErrorCollector {
  errors: Record<string, string> = {};
  count: number = 0;

  getCount() { return this.count; }

  getErrors() { return this.errors; }

  collect(name: string, error: string) {
    if (!this.errors[name]) {
      this.errors[name] = error;
      this.count++;
    }
  }

  remove(name: string) {
    if (this.errors[name]) {
      delete this.errors[name];
      this.count--;
    }
  }

  assertNoError(title: string) {
    if (this.count > 0) {
      let fieldMessages = [];
      for (let name in this.errors) {
        fieldMessages.push(
          <div className='text-warning'>
            {`field ${name} has error ${this.errors[name]}`}
          </div>
        );
      }
      let detail = <div>{fieldMessages}</div>;
      notificationShow('warning', title, detail);
      return false
    }
    return true;
  }

  dump() {
    console.log('ErrorCollector:');
    console.log('  count = ' + this.count);
    for (let name in this.errors) {
      console.log(`  ${name} = ${this.errors[name]}`);
    }
  }
}

export class InputChange {
  name: string;
  oldValue: any;
  newValue: any;
  desc: any;

  constructor(name: string, oldValue: any, newValue: any, desc: any = null) {
    this.name = name;
    this.oldValue = oldValue;
    this.newValue = newValue;
    this.desc = desc;
  }
}

export class InputChangeCollector {
  changes: Record<string, InputChange> = {};
  count: number = 0;
  lastModifiedTime: number = 0;

  addChange(name: string, oldVal: any, newVal: any, desc: any = null) {
    if (oldVal === newVal || (!oldVal && !newVal)) return;
    if (this.changes[name] != null) {
      this.changes[name].newValue = newVal;
      this.changes[name].desc = desc;
    } else {
      let change = new InputChange(name, oldVal, newVal, desc);
      this.changes[name] = change;
      this.count++;
    }
    this.lastModifiedTime = Date.now();
  }

  getChanges(): Array<InputChange> { return Object.values(this.changes) };

  getLastModifiedTime(): number { return this.lastModifiedTime };

  getChangeCount(): number { return this.count; }

  clear() {
    this.changes = {};
    this.count = 0;
  }
}

export interface InputObserver {
  getErrorCollector: () => ErrorCollector;
  getInputChangeCollector: () => InputChangeCollector;
}

function runInputValidation(value: any, validators?: Array<Validator>) {
  if (!validators) return;
  for (let i = 0; i < validators.length; i++) {
    validators[i].validate(value);
  }
}
export interface WInputProps extends ELEProps {
  inputId?: string;
  name: string;
  value: any;
  defaultValue?: string;
  placeholder: any;
  disable?: boolean;
  focus?: boolean;
  tabIndex?: number;
  required?: boolean;
  validators?: Array<Validator>;
  inputObserver?: InputObserver;
  onChange?: (oldVal: any, newVal: any) => void;
  onInputChange?: (oldVal: any, newVal: any) => void;
  onBgValidate?: (wInput: WInput, oldVal: any, newVal: any) => void;
  onKeyDown?: (winput: WInput, event: KeyboardEvent, currInput: any) => void;
  onRefreshAction?: (winput: WInput) => void;
  mapDisplayValue?: (inputValue: any) => any;
  mapInputValue?: (displayValue: any) => any;
};
export interface WInputState {
  message: null | string;
  value: any;
  inputValue: any;
  focus: boolean
};
export class WInput<T extends WInputProps = WInputProps> extends Component<T, WInputState> {
  customClass: undefined | string = undefined;
  message: null | string = null;

  static getDerivedStateFromProps(props: WInputProps, state: WInputState) {
    let { name, value, defaultValue, required, inputObserver } = props;
    if (state.value !== value) {
      if (inputObserver) inputObserver.getErrorCollector().remove(name);
      if (!defaultValue) defaultValue = '';
      let inputValue = value ? value : defaultValue;
      let newState: WInputState = {
        message: null, value: value, inputValue: inputValue, focus: props.focus ? true : false
      };
      if (required && inputValue == '') {
        newState.message = 'This field cannot be empty';
        if (inputObserver) inputObserver.getErrorCollector().collect(name, newState.message);
      }
      return newState;
    }
    return null;
  }

  constructor(props: T) {
    super(props);

    this.state = { message: null, value: null, inputValue: null, focus: props.focus ? true : false };
    this.onPostInit(props);
  }

  // componentWillUnmount(): void {
  //   let { value, inputValue } = this.state;
  //   if (inputValue === '') inputValue = null;
  //   if (value === '' || value === 0) value = null;
  //   if (value != inputValue) {
  //     this.updateValue(inputValue, false);
  //   }
  // }

  onPostInit(_props: WInputProps) { }

  getMessage() { return this.message; }

  onFocus = (evt: any) => {
    if (this.props.disable) return;
    evt.target.select();
    evt.stopPropagation();
    this.setState({ message: '', focus: true });
  }

  onFocusLost = (_evt: any) => {
    if (this.props.disable) return;
    this.updateValue(this.state.inputValue, false);
  }

  onChange = (e: any) => {
    let oldVal = this.state.inputValue;
    let { onChange } = this.props;
    this.setState({ inputValue: e.target.value });
    if (onChange) onChange(oldVal, e.target.value);
  }

  _isEmpty(val: any) {
    if (!val) return true;
    if (typeof val === 'string') {
      return val.length === 0;
    }
    return false;
  }

  updateValue(newVal: any, focus: boolean = true) {
    const { name, required, validators, inputObserver, onBgValidate, onInputChange, mapInputValue } = this.props;
    if (mapInputValue) newVal = mapInputValue(newVal);
    let oldVal: any = this.state.value;
    // if (oldVal == undefined) oldVal = '';
    if (oldVal == undefined) oldVal = null;
    if (newVal == oldVal) {
      this.setState({ focus: focus });
      return;
    }
    if (inputObserver) {
      inputObserver.getErrorCollector().remove(name);
    }
    try {
      if (newVal && newVal.trim) newVal = newVal.trim();
      if (required && this._isEmpty(newVal)) {
        let errMsg = 'This field cannot be empty';
        if (inputObserver) inputObserver.getErrorCollector().collect(name, errMsg);
        this.setState({ message: errMsg, inputValue: oldVal, focus: focus });
        return;
      }
      let val = this.convert(newVal);
      this.message = null;
      runInputValidation(val, validators);
      this.setState({ message: null, value: val, inputValue: val, focus: focus });
      if (inputObserver) {
        inputObserver.getInputChangeCollector().addChange(name, oldVal, newVal);
      }
      if (onInputChange) onInputChange(oldVal, val);
      if (onBgValidate) onBgValidate(this, oldVal, newVal);
    } catch (err) {
      let errMsg = err.message;
      if (inputObserver) inputObserver.getErrorCollector().collect(name, errMsg);
      this.setState({ message: errMsg, inputValue: oldVal, focus: focus });
      this.message = errMsg;
    }
  }

  convert(_newVal: string) { throw new Error('this method need to be implemented'); }

  setError(inputValue: any, errMsg: string) {
    const { name, inputObserver } = this.props;
    if (inputObserver) inputObserver.getErrorCollector().collect(name, errMsg);
    this.message = errMsg;
    this.setState({ message: errMsg, inputValue: inputValue });
  }

  onKeyDown = (evt: KeyboardEvent) => {
    let key = evt.key;
    //Ignore and use in the table navigation
    if (evt.shiftKey && key.startsWith('Arrow')) {
      //this.onFocusLost(evt);
      return;
    }
    let { onKeyDown } = this.props;
    if (onKeyDown) {
      let currInput = this.state.inputValue;
      onKeyDown(this, evt, currInput);
    }
  }

  toDisplayValue(value: any) {
    if (!value) return '';
    const { mapDisplayValue } = this.props;
    if (mapDisplayValue) value = mapDisplayValue(value);
    return value;
  }

  _getInputType() { return 'text'; }

  _getCustomClass() { return null; }

  render() {
    let { style, className, tabIndex, name, placeholder, disable, onRefreshAction, inputId } = this.props;
    let { inputValue, focus, message } = this.state;
    let displayValue = this.toDisplayValue(inputValue);
    let classes = mergeCssClass(className, `form-control`);
    let type = this._getInputType();
    if (!focus && message) {
      displayValue = message;
      classes = mergeCssClass(classes, 'is-invalid');
      type = 'text';
    }
    classes = mergeCssClass(classes, this.customClass);
    let inputUI = (
      <input id={inputId} style={style} tabIndex={tabIndex} className={classes} autoFocus={this.props.focus} type={type}
        name={name} value={displayValue} placeholder={placeholder} disabled={disable} autoComplete="off"
        onChange={this.onChange} onFocus={this.onFocus} onBlur={this.onFocusLost} onKeyDown={this.onKeyDown} />
    );
    if (onRefreshAction && !disable) {
      return (
        <div className='flex-hbox'>
          {inputUI}
          <Button laf='link' className='p-1' onClick={() => onRefreshAction ? onRefreshAction(this) : undefined}>
            <FeatherIcon.RefreshCw size={12} />
          </Button>
        </div>
      );
    }
    return inputUI;
  }
}

export class WStringInput extends WInput {
  convert(newVal: string) { return newVal; }
}

export class WPasswordInput extends WStringInput {
  type: 'text' | 'password' = 'password';

  _getInputType() { return this.type; }

  onToggleVisibility = () => {
    if (this.type === 'password') this.type = 'text';
    else this.type = 'password';
    this.forceUpdate();
  }

  render() {
    let inputHtml = super.render();
    let html = (
      <div className='flex-hbox'>
        {inputHtml}
        <button type='button' className='btn btn-link p-1' onClick={this.onToggleVisibility}>
          <FeatherIcon.Eye size={12} />
        </button>
      </div>
    );
    return html;
  }
}

export class WTextInput extends WStringInput {
  render() {
    let { style, className, name, placeholder, disable, tabIndex } = this.props;

    let { inputValue, focus, message } = this.state;
    let displayValue = this.toDisplayValue(inputValue);
    if (this.state.message) displayValue = this.state.message;
    let classes = mergeCssClass(className, `form-control`);
    if (!focus && message) {
      displayValue = message;
      classes = mergeCssClass(classes, 'is-invalid');
    }
    classes = mergeCssClass(classes, this.customClass);

    let html = (
      <textarea
        style={style} className={classes} tabIndex={tabIndex} autoFocus={focus}
        name={name} value={displayValue} placeholder={placeholder} disabled={disable}
        onChange={this.onChange} onFocus={this.onFocus} onBlur={this.onFocusLost} onKeyDown={this.onKeyDown}></textarea>
    );
    return html;
  }
}

export class WIntInput extends WInput {
  onPostInit(_props: WInputProps) { this.customClass = 'text-end'; }

  convert(newVal: string) {
    if (isNormalInteger(newVal)) return parseInt(newVal, 10);
    throw new Error(newVal + ' is not a number');
  }
}

export class WLongInput extends WInput {
  onPostInit(_props: WInputProps) { this.customClass = 'text-end'; }

  convert(value: string) {
    if (isNormalInteger(value)) return parseInt(value, 10);
    throw new Error(value + ' is not a long number');
  }
}

export class WFloatInput extends WInput {
  onPostInit(_props: WInputProps) { this.customClass = 'text-end'; }

  convert(value: string) {
    if (!value) return '';
    if (isDecimal(value)) return parseFloat(value);
    throw new Error(value + ' is not a float number');
  }
}

export class WDoubleInput<T extends WInputProps = WInputProps> extends WInput<T> {
  onPostInit(_props: WInputProps) { this.customClass = 'text-end'; }

  convert(value: string) {
    value = value.replace(/,/g, '');
    if (!value) return 0;
    if (value.startsWith("=")) {
      let val = value.slice(1);
      try {
        eval(`val = ${val}`);
        return parseFloat(val);
      } catch (error) {
        throw new Error(`${val} not calculate`);
      }
    }
    if (isDecimal(value)) return parseFloat(value);
    throw new Error(`${value} is not a double number`);
  }
}

interface WNumberInputProps extends WInputProps {
  precision?: number;
  maxPrecision?: number;
}
export class WNumberInput<T extends WNumberInputProps = WNumberInputProps> extends WDoubleInput<T> {
  toDisplayValue(value: any) {
    let { mapDisplayValue, precision, maxPrecision } = this.props;
    if (mapDisplayValue) value = mapDisplayValue(value);
    if (!value || typeof value === 'string' || value instanceof String) return value;

    if (precision) return formater.number(value, precision);
    if (!maxPrecision) {
      maxPrecision = 3;
    }
    let precisionCount = 0;
    if (Math.floor(value) != value) {
      precisionCount = value.toString().split(".")[1].length || 0;
    }
    /** @ts-ignore */
    if (precisionCount > maxPrecision) {
      return formater.number(value, maxPrecision);
    }
    return formater.number(value, precisionCount);
  }
}

export class WPercentInput extends WInput {
  onPostInit(_props: WInputProps) { this.customClass = 'text-end'; }

  convert(value: string) {
    value = value.replace(/%/g, '');
    value = value.replace(/,/g, '');
    if (!value) return 0;
    if (isDecimal(value)) return parseFloat(value) / 100;
    throw new Error(value + ' is not a double number');
  }

  toDisplayValue(value: any) {
    if (!value) return '0';
    if (!value || typeof value === 'string' || value instanceof String) return value;
    return formater.percent(value);
  }
}

export interface WArrayInputProps {
  name: string;
  value: any;
  placeholder: any;
  disable?: boolean;
  disableAdd?: boolean;
  validators?: Array<Validator>;
  inputObserver?: InputObserver;
  onChange?: any;
  onInputChange: any;
  onKeyDown?: any
};
export interface WArrayInputState {
  inputValues: Array<any>;
  propsValues: Array<any> | null;
};
class WArrayInput extends Component<WArrayInputProps, WArrayInputState> {
  state: WArrayInputState = { propsValues: null, inputValues: [] };
  addNew: boolean = false;

  static getDerivedStateFromProps(nextProps: WArrayInputProps, prevState: WArrayInputState) {
    let values = nextProps.value;
    if (values != prevState.propsValues) {
      let inputValues = [];
      if (values != null && values.length > 0) {
        for (let i = 0; i < values.length; i++) {
          inputValues.push({ message: null, value: values[i], inputValue: values[i] });
        }
      }
      let newState: WArrayInputState = { inputValues: inputValues, propsValues: values };
      return newState;
    }
    return prevState;
  }

  getInputClass(): string | undefined { return undefined; }

  onFocus(idx: number, _evt: any) {
    if (this.addNew) {
      this.addNew = false;
      return;
    }
    let inputValues = this.state.inputValues;
    inputValues[idx].message = null;
    this.setState({ inputValues: inputValues });
    this.forceUpdate();
  }

  onFocusLost(idx: number, evt: any) {
    const { name, validators, inputObserver, onInputChange } = this.props;
    let { inputValues } = this.state;
    let newVal = this.convert(evt.target.value.trim());

    try {
      if (validators != null) {
        for (let i = 0; i < validators.length; i++) {
          validators[i].validate(newVal);
        }
      }
      if (inputObserver) inputObserver.getErrorCollector().remove(name);

      inputValues[idx].value = newVal;

      if (onInputChange) {
        var values = [];
        for (let i = 0; i < inputValues.length; i++) {
          values.push(inputValues[i].value);
        }
        onInputChange(this.state.propsValues, values);
        this.setState({ propsValues: values });
      }
    } catch (err) {
      let inputValues = this.state.inputValues;
      inputValues[idx].message = err.toString();
      this.setState({ inputValues: inputValues });
    }
  }

  convert(newVal: string): any { return newVal; }

  onChange(idx: number, e: any) {
    const { name, inputObserver } = this.props;
    let inputValues = this.state.inputValues;
    let oldVal = inputValues[idx].inputValue;
    let newVal = e.target.value;
    inputValues[idx].inputValue = newVal;
    this.setState({ inputValues: inputValues });
    if (inputObserver) {
      inputObserver.getInputChangeCollector().addChange(name, oldVal, newVal);
    }
  }

  onRemove(idx: number) {
    let { inputValues } = this.state;
    inputValues.splice(idx, 1);

    const { onInputChange, name, inputObserver } = this.props;
    if (onInputChange) {
      var values = [];
      for (let i = 0; i < inputValues.length; i++) {
        values.push(inputValues[i].value);
      }
      onInputChange(this.state.propsValues, values);
    }
    this.setState({ inputValues: inputValues });
    if (inputObserver) {
      inputObserver.getInputChangeCollector().addChange(name, '', '');
    }
  }

  onAddNew = (_evt: any) => {
    const { name, inputObserver } = this.props;
    let { inputValues } = this.state;
    inputValues.push({ message: null, value: '', inputValue: '' });
    this.setState({ inputValues: inputValues });
    this.addNew = true;
    if (inputObserver) {
      inputObserver.getInputChangeCollector().addChange(name, '', '');
    }
  }

  isFieldEditable() { return true; }

  render() {
    const { placeholder, disable, onKeyDown } = this.props;
    let readOnly = !this.isFieldEditable() || disable ? true : false;
    let inputValues = this.state.inputValues;
    let inputs = [];
    let renderDelete = null;
    for (let i = 0; i < inputValues.length; i++) {
      let ivalue = inputValues[i];
      let inputValue = ivalue.inputValue;
      if (ivalue.message) inputValue = ivalue.message;
      if (!readOnly) {
        renderDelete = (
          <Button laf='link' className='p-1' onClick={() => this.onRemove(i)}>
            <FeatherIcon.Minus size={12} />
          </Button>
        );
      }
      let autoFocus = false;
      if (this.addNew && i == inputValues.length - 1) {
        autoFocus = true;
      }
      let inputClass = mergeCssClass('form-control', this.getInputClass());
      inputs.push((
        <div key={i} className='flex-hbox py-1'>
          <input className={inputClass} type={'text'} name={`name${i}`} value={inputValue} placeholder={placeholder}
            disabled={readOnly} onChange={(e) => this.onChange(i, e)} onFocus={(e) => this.onFocus(i, e)}
            onBlur={(e) => this.onFocusLost(i, e)} onKeyDown={onKeyDown} autoComplete='off' autoFocus={autoFocus} />
          {renderDelete}
        </div>
      ));
    }
    let html = (
      <div className='flex-vbox'>
        {inputs}
        {this.renderAdd(readOnly)}
      </div>
    );
    return html;
  }

  renderAdd(readOnly: boolean) {
    if (readOnly) return null;
    const { disableAdd } = this.props;
    if (disableAdd) return null;
    let html = (
      <div className='flex-hbox justify-content-end'>
        <Button laf='link' className='p-1' onClick={this.onAddNew}>
          <FeatherIcon.Plus size={12} />
        </Button>
      </div>
    );
    return html;
  }
}

export class WStringArrayInput extends WArrayInput {
  convert(newVal: string) { return newVal; }
}

export class WIntArrayInput extends WArrayInput {
  getInputClass(): string | undefined { return 'text-end'; }

  convert(newVal: string): any {
    if (isNormalInteger(newVal)) return parseInt(newVal, 10);
    throw new Error(newVal + ' is not a number');
  }
}

export class WLongArrayInput extends WArrayInput {
  getInputClass(): string | undefined { return 'text-end'; }

  convert(value: string): any {
    if (isNormalInteger(value)) return parseInt(value, 10);
    throw new Error(value + ' is not a long number');
  }
}

export class WFloatArrayInput extends WArrayInput {
  getInputClass(): string | undefined { return 'text-end'; }

  convert(value: string): any {
    if (isDecimal(value)) return parseFloat(value);
    throw new Error(value + ' is not a float number');
  }
}

export class WDoubleArrayInput extends WArrayInput {
  getInputClass(): string | undefined { return 'text-end'; }

  convert(value: string): any {
    if (isDecimal(value)) return parseFloat(value);
    throw new Error(value + ' is not a double number');
  }
}

export interface WRadioInputProps extends ELEProps {
  name: string;
  select: any;
  options: Array<any>;
  optionLabels?: Array<string | ReactElement>;
  disable?: boolean;
  onInputChange: (oldVal: string, newVal: string) => void;
  inputObserver?: InputObserver;
}
export interface WRadioInputState { select: string }
export class WRadioInput extends Component<WRadioInputProps, WRadioInputState> {
  static getDerivedStateFromProps(nextProps: WRadioInputProps, prevState: WRadioInputState) {
    if (prevState.select !== nextProps.select) {
      return { select: nextProps.select };
    }
    return null;
  }

  constructor(props: WRadioInputProps) {
    super(props);
    const { select } = props;
    this.state = { select: select };
  }

  onSelect(idx: number) {
    const { name, inputObserver, onInputChange, options } = this.props;
    let oldVal = this.state.select;
    let newVal = options[idx];
    if (onInputChange) onInputChange(oldVal, newVal);
    this.setState({ select: newVal });
    if (inputObserver) {
      inputObserver.getInputChangeCollector().addChange(name, oldVal, newVal);
    }
  }

  render() {
    let { name, options, optionLabels, disable, style, className } = this.props;
    let { select } = this.state;
    if (!optionLabels) optionLabels = options;
    let inputBlocks = [];
    for (let i = 0; i < options.length; i++) {
      let option = options[i];
      if (!option) option = '';
      inputBlocks.push(
        <div key={i} className='col p-1 text-nowrap'>
          <input type={'radio'} name={name} value={option} disabled={disable}
            checked={option === select} onChange={() => this.onSelect(i)} />
          <span className='px-1 d-inline-block'>{optionLabels[i]}</span>
        </div>
      );
    }
    //if (!style) style = { display: 'flex', flexFlow: 'row wrap', width: '100%' }
    className = mergeCssClass('row g-0 mx-0', className)
    let html = (
      <div className={className} style={style}> {inputBlocks} </div>
    );
    return html;
  }

}

export interface WCheckboxInputProps extends ELEProps {
  name: string;
  label?: string;
  checked: boolean;
  disable?: boolean;
  focus?: boolean;
  tabIndex?: number;
  inputObserver?: InputObserver;
  onInputChange: (checked: boolean) => void;
}
export interface WCheckboxInputState { checked: boolean }
export class WCheckboxInput extends Component<WCheckboxInputProps, WCheckboxInputState> {
  static getDerivedStateFromProps(nextProps: WCheckboxInputProps, prevState: WCheckboxInputState) {
    if (prevState.checked !== nextProps.checked) {
      return { checked: nextProps.checked };
    }
    return null;
  }

  constructor(props: WCheckboxInputProps) {
    super(props);
    this.state = { checked: props.checked };
  }

  onSelect = (evt: ChangeEvent) => {
    evt.stopPropagation();
    let tartget: any = evt.target;
    tartget.select();
    const { name, onInputChange, inputObserver } = this.props;
    let checked = !this.state.checked;
    this.setState({ checked: checked });
    if (inputObserver) {
      inputObserver.getInputChangeCollector().addChange(name, !checked, checked);
    }
    if (onInputChange) onInputChange(checked);
  }

  render() {
    let { className, style, name, label, focus, tabIndex, disable } = this.props;
    const { checked } = this.state;
    let autoFocus = focus ? true : false;
    if (!label) {
      return (
        <input
          className={className} style={style} tabIndex={tabIndex} autoFocus={autoFocus}
          type='checkbox' name={name} value={name} disabled={disable} checked={checked}
          onChange={this.onSelect} />
      );
    }
    let html = (
      <div className={mergeCssClass('flex-hbox-grow-0 align-items-center text-nowrap', className)} style={style}>
        <input
          type='checkbox' name={name} value={name} disabled={disable} checked={checked}
          autoFocus={autoFocus} tabIndex={tabIndex} onChange={this.onSelect} />
        <span className='mx-1' style={{ display: 'inline-block' }}>{label}</span>
      </div>
    );
    return html;
  }
}

export interface WMultiCheckboxInputProps {
  name: string;
  select: Array<string>;
  options: Array<any>;
  optionLabels?: Array<string>;
  disable?: boolean;
  onInputChange: (oldVal: Array<string>, newVal: Array<string>) => void;
  inputObserver?: InputObserver;
}
export interface WMultiCheckboxInputState {
  select: Array<string>;
  checked: Array<boolean>;
  init: boolean;
}
export class WMultiCheckboxInput extends Component<WMultiCheckboxInputProps, WMultiCheckboxInputState> {
  static getDerivedStateFromProps(nextProps: WMultiCheckboxInputProps, prevState: WMultiCheckboxInputState) {
    if (!prevState.init) {
      const { select, options } = nextProps;
      let checked = [];
      for (let i = 0; i < options.length; i++) {
        checked[i] = false;
        if (select) {
          for (let j = 0; j < select.length; j++) {
            if (options[i] == select[j]) {
              checked[i] = true;
              break;
            }
          }
        }
      }
      prevState.init = true;
      prevState.checked = checked;
    }
    return prevState;
  }

  constructor(props: WMultiCheckboxInputProps) {
    super(props);
    let { select } = props;
    this.state = { select: select, checked: [], init: false };
  }

  onSelect(idx: number) {
    const { onInputChange, options, name, inputObserver } = this.props;
    let oldSelects = this.state.select;
    this.state.checked[idx] = !this.state.checked[idx];
    let newSelects = [];
    for (let i = 0; i < options.length; i++) {
      if (this.state.checked[i]) {
        newSelects.push(options[i]);
      }
    }
    if (onInputChange) onInputChange(oldSelects, newSelects);
    this.setState({ select: newSelects });
    if (inputObserver) {
      inputObserver.getInputChangeCollector().addChange(name, oldSelects, newSelects);
    }
  }

  render() {
    let { name, options, optionLabels, disable } = this.props;
    let { checked } = this.state;
    if (!optionLabels) optionLabels = options;
    let inputBlocks = [];
    for (let i = 0; i < options.length; i++) {
      let option = options[i];
      inputBlocks.push(
        <div key={i} className='col-4 flex-hbox align-items-center px-1'>
          <input type='checkbox' name={name} value={option} disabled={disable} checked={checked[i]} onChange={() => this.onSelect(i)} />
          <div className='d-inline-block mx-1'>{optionLabels[i]}</div>
        </div>
      );
    }
    let html = (
      <div className='row g-1 p-1'>
        {inputBlocks}
      </div>
    );
    return html;
  }
}

export interface WSelectProps extends ELEProps {
  name: string;
  options: Array<any>;
  optionLabels?: Array<any>;
  select: any;
  disable?: boolean;
  focus?: boolean;
  tabIndex?: number;
  onSelect?: (option: any) => void;
  inputObserver?: InputObserver;
};
export interface WSelectState { select: null | any };
export class WSelect extends Component<WSelectProps, WSelectState> {

  static getDerivedStateFromProps(nextProps: WSelectProps, prevState: WSelectState) {
    if (prevState.select !== nextProps.select) {
      return { select: nextProps.select };
    }
    return null;
  }

  constructor(props: WSelectProps) {
    super(props);
    this.state = { select: props.select };
  }

  onChange = (event: any) => {
    const { options, name, inputObserver } = this.props;
    let value = event.target.value;
    for (let i = 0; i < options.length; i++) {
      if (options[i] == value) {
        if (this.props.onSelect) this.props.onSelect(options[i]);
        break;
      }
    }
    this.setState({ select: value });
    if (inputObserver) {
      inputObserver.getInputChangeCollector().addChange(name, '', value);
    }
  }

  render() {
    const { options, className, style, disable, optionLabels, focus, tabIndex, name } = this.props;
    const { select } = this.state;
    let optionHtml = [];

    for (let i = 0; i < options.length; i++) {
      let label = options[i];
      if (optionLabels) { label = optionLabels[i]; }
      optionHtml.push((<option key={i} value={options[i]}>{label}</option>));
    }
    let cssClass = mergeCssClass('form-control', className);
    let html = (
      <select tabIndex={tabIndex} className={cssClass} style={style} disabled={disable}
        name={name} onChange={this.onChange} value={select} autoFocus={focus ? true : false}>
        {optionHtml}
      </select>
    );
    return html;
  }
}

export interface WBeanSelectProps {
  name: string;
  options: Array<any>;
  fieldLabel: string;
  fieldCheck: string;
  select: any;
  disable?: boolean, style?: string;
  onSelect: (value: number) => void;
  inputObserver?: InputObserver;
};
export interface WBeanSelectState { select: any };
export class WBeanSelect extends Component<WBeanSelectProps, WBeanSelectState> {

  static getDerivedStateFromProps(nextProps: WSelectProps, prevState: WSelectState) {
    if (prevState.select !== nextProps.select) {
      return { select: nextProps.select };
    }
    return null;
  }

  constructor(props: WBeanSelectProps) {
    super(props);
    this.state = { select: this.props.select };
  }

  onSelect(idx: number) {
    let { options, fieldCheck, onSelect, name, inputObserver } = this.props;
    let newOpt = options[idx];
    setTimeout(() => {
      if (onSelect) onSelect(newOpt);
    }, 200)
    this.setState({ select: newOpt[fieldCheck] });
    if (inputObserver) {
      inputObserver.getInputChangeCollector().addChange(name, null, newOpt);
    }
  }

  render() {
    const { options, fieldLabel, fieldCheck, name } = this.props;
    let { select } = this.state;
    let optHtml = [];
    let selectLabel = select;
    for (let i = 0; i < options.length; i++) {
      let opt = options[i];
      let checked = opt[fieldCheck] === select;
      if (checked) selectLabel = opt[fieldLabel];
      optHtml.push((
        <div key={`item-${i}`} className='flex-hbox px-1'>
          <input type="checkbox" name={name} checked={checked} onChange={() => this.onSelect(i)} />
          <label className='py-1 ps-2'>{opt[fieldLabel]}</label>
        </div>
      ));
    }
    let dropdownId = 'wbean-select';
    let htmlTemplate = (
      <bs.Dropdown>
        <bs.DropdownToggle dropdownId={dropdownId} laf='link'>
          {selectLabel}
        </bs.DropdownToggle>
        <bs.DropdownContent dropdownId={dropdownId}>
          {optHtml}
        </bs.DropdownContent>
      </bs.Dropdown>
    );
    return htmlTemplate;
  }
}

export interface WDateTimeProps {
  className?: string;
  style?: any;
  name: string;
  value: any;
  dateFormat?: string;
  timeFormat: any;
  disable?: boolean;
  focus?: boolean;
  tabIndex?: number;
  required?: boolean;
  validators?: Array<Validator>;
  inputObserver?: InputObserver;
  onCommitChange?: (moment: any) => void;
};
export interface WDateTimeState {
  propsValue: any;
  value: Date | string;
  inputValue: Date | string;
  errorMessage: string;
  focus: boolean;
};
export class WDateTime extends Component<WDateTimeProps, WDateTimeState> {
  state = { propsValue: '___', value: '', inputValue: '', errorMessage: '', focus: false, open: false }
  timeView = false;
  focusTime: number = -1;
  time: moment.Moment | null = null;


  static getDerivedStateFromProps(props: WDateTimeProps, state: WDateTimeState) {
    let { name, required, value, inputObserver, focus } = props;
    if (state.propsValue != value) {
      let errorMessage = '';
      if (required && (!value || value === '')) {
        errorMessage = 'This field is empty or invalid format';
        if (inputObserver) inputObserver.getErrorCollector().collect(name, errorMessage);
      }
      let newState: WDateTimeState = {
        propsValue: value, value: value, inputValue: value, errorMessage: errorMessage,
        focus: focus ? true : false
      };
      return newState;
    }
    return null;
  }

  onChange = (date: moment.Moment) => {
    if (this.timeView) {
      this.time = date;
      return;
    }
    if (this.time != null) {
      date = date.set({ hour: this.time.get('hour'), minute: this.time.get('minute'), second: this.time.get('second') });
      this.time = null;
    }
    this.updateValue(date);
  }

  onFocus = (evt: any) => {
    this.timeView = false;
    this.focusTime = new Date().getTime();
    evt.target.select();
    this.setState({ errorMessage: '', focus: true });
  }

  onFocusLost = (_evt: any) => {
    this.setState({ focus: false });
  }

  onBeforeNavigate = (nextView: string, _currentView: string, _viewDate: any) => {
    if (nextView == 'time') {
      this.timeView = true;
    } else {
      this.timeView = false;
    }
    return nextView;
  }

  updateValue(inputValue: any) {
    let { name, validators, inputObserver, dateFormat, required } = this.props;

    if (inputObserver) inputObserver.getErrorCollector().remove(name);
    if (!inputValue || inputValue === '') {
      if (required) {
        let errorMessage = 'This field is empty or invalid format';
        if (inputObserver) inputObserver.getErrorCollector().collect(name, errorMessage);
        this.setState({ inputValue: inputValue, errorMessage: errorMessage });
      } else {
        if (this.commitChange(null)) {
          this.setState({ value: '', inputValue: '', errorMessage: '' });
        }
      }
      return;
    }
    let parseValue: any = null;
    if (typeof inputValue === 'string' || inputValue instanceof String) {
      inputValue = inputValue as string;
      parseValue = moment(inputValue).format(dateFormat);
    } else {
      parseValue = moment(inputValue, dateFormat);
    }
    if (isNaN(parseValue)) {
      let errorMessage = 'This field is empty or invalid format';
      if (inputObserver) inputObserver.getErrorCollector().collect(name, errorMessage);
      this.setState({ errorMessage: errorMessage });
      return;
    }

    try {
      runInputValidation(parseValue, validators);
    } catch (err) {
      let errorMessage = err.message;
      this.setState({ inputValue: inputValue, errorMessage: errorMessage });
      if (inputObserver) inputObserver.getErrorCollector().collect(name, errorMessage);
      return;
    }
    if (this.commitChange(parseValue)) {
      this.setState({ value: parseValue, inputValue: inputValue, errorMessage: '' });
      if (inputObserver) {
        inputObserver.getInputChangeCollector().addChange(name, null, inputValue);
      }
    }
  }

  commitChange(value: any) {
    let { onCommitChange, inputObserver, name } = this.props;
    try {
      if (onCommitChange) onCommitChange(value);
      return true;
    } catch (error: any) {
      let errorMessage = error.message;
      alert(errorMessage);
      if (inputObserver) inputObserver.getErrorCollector().collect(name, errorMessage);
      this.setState({ value: '', inputValue: '', errorMessage: errorMessage });
    }
    return false;
  }

  render() {
    let { className, dateFormat, timeFormat, disable, tabIndex } = this.props;
    let { inputValue, errorMessage, focus } = this.state;
    if (!dateFormat) dateFormat = 'DD/MM/YYYY';
    if (!timeFormat) timeFormat = false;
    className = mergeCssClass(className, 'flex-hbox justify-content-end');
    if (errorMessage) {
      className = mergeCssClass(className, 'form-control-date-error');
    }
    return (
      <div key={`${inputValue}`} className={className} style={{ position: 'relative' }}>
        <DateTime
          value={inputValue} dateFormat={dateFormat} timeFormat={timeFormat}
          inputProps={{
            disabled: disable, onFocus: this.onFocus, onBlur: this.onFocusLost, autoFocus: focus, tabIndex: tabIndex,
          }}
          closeOnSelect={true} closeOnClickOutside={true}
          onBeforeNavigate={this.onBeforeNavigate} onChange={this.onChange} />
      </div>
    );
  }
}

export interface WDateInputMaskProps extends ELEProps {
  name: string;
  value: string;
  format: 'DD/MM/YYYY' | 'MM/DD/YYYY';
  timeFormat?: boolean;
  tabIndex?: number;
  disabled?: boolean;
  onChange?: (value: moment.Moment | null) => void;
  inputObserver?: InputObserver;
}
interface WDateInputMaskState {
  initialValue: string;
  value: string;
}
export class WDateInputMask extends Component<WDateInputMaskProps, WDateInputMaskState> {
  inputRef: React.RefObject<HTMLInputElement>;
  mask: InputMask<any> | null = null;

  constructor(props: WDateInputMaskProps) {
    super(props);
    this.state = { initialValue: props.value, value: props.value };
    this.inputRef = React.createRef();
  }

  componentDidMount() {
    const { format, timeFormat } = this.props;
    // const maskPattern = timeFormat ? '00/00/0000 00:00' : '00/00/0000';

    let dateFormat: string = format;
    if (timeFormat) dateFormat = `${format} HH:mm`;

    if (this.inputRef.current) {
      const maskOptions: FactoryArg = {
        mask: Date,
        pattern: timeFormat ? dateFormat : "d{/}`m{/}`Y",
        lazy: false,
        overwrite: true,
        blocks: {
          DD: {
            mask: IMask.MaskedRange,
            from: 1,
            to: 31,
            maxLength: 2,
          },
          MM: {
            mask: IMask.MaskedRange,
            from: 1,
            to: 12,
            maxLength: 2,
          },
          YYYY: {
            mask: IMask.MaskedRange,
            from: 1900,
            to: 9999,
          },
          HH: {
            mask: IMask.MaskedRange,
            from: 0,
            to: 23,
            maxLength: 2,
          },
          mm: {
            mask: IMask.MaskedRange,
            from: 0,
            to: 59,
            maxLength: 2,
          },
        },
        format: (date: Date) => moment(date).format(dateFormat),
        parse: (str: string) => moment(str, dateFormat).toDate(),
      }
      this.mask = IMask(this.inputRef.current, maskOptions);

    }
  }

  componentWillUnmount() {
    if (this.mask) {
      this.mask.destroy();
    }
  }

  onFocusLost = (evt: any) => {
    let { onChange, inputObserver, name, format, timeFormat } = this.props;
    let value = evt.target.value;
    let momentDate = null;
    let dateFormat: string = format;
    if (timeFormat) dateFormat = `${format} HH:mm`;

    if (value.trim() === '' || value === '__/__/____' || (timeFormat && value === '__/__/____ __:__')) {
      this.setState({ value: '' });
      if (inputObserver) {
        inputObserver.getInputChangeCollector().addChange(name, this.state.initialValue, '');
      }
      if (onChange) onChange(null);
      return;
    }

    const now = moment();
    const parts = value.split(/[/\s:]/).filter((part: string) => !part.includes('_'));

    // Handle different date formats (DD/MM/YYYY or MM/DD/YYYY)
    const isMMDDFormat = format === 'MM/DD/YYYY';
    let day, month, year, hour, minute;

    if (parts.length > 0) {
      if (isMMDDFormat) {
        month = parts[0] ? parseInt(parts[0]) : now.month() + 1;
        day = parts[1] ? parseInt(parts[1]) : now.date();
      } else {
        day = parts[0] ? parseInt(parts[0]) : now.date();
        month = parts[1] ? parseInt(parts[1]) : now.month() + 1;
      }
      year = parts[2] ? parseInt(parts[2]) : now.year();

      if (year < 100) {
        year += year < 50 ? 2000 : 1900;
      }

      if (timeFormat && parts.length > 3) {
        hour = parts[3] ? parseInt(parts[3]) : now.hour();
        minute = parts[4] ? parseInt(parts[4]) : 0;
      }
    }

    day = Math.min(Math.max(day || 1, 1), 31);
    month = Math.min(Math.max(month || 1, 1), 12);
    year = Math.min(Math.max(year || now.year(), 1900), 9999);

    if (timeFormat) {
      hour = Math.min(Math.max(hour || 0, 0), 23);
      minute = Math.min(Math.max(minute || 0, 0), 59);
      momentDate = moment({ year, month: month - 1, date: day, hour, minute });
    } else {
      momentDate = moment({ year, month: month - 1, date: day });
    }

    if (!momentDate.isValid()) {
      this.setState({ value: this.state.initialValue });
      return;
    }

    const formattedValue = momentDate.format(dateFormat);
    this.setState({ value: formattedValue });

    if (inputObserver) {
      inputObserver.getInputChangeCollector().addChange(name, this.state.initialValue, formattedValue);
    }
    if (onChange) onChange(momentDate);
  }

  onAccept = (value: string) => {
    if (value.includes('_')) this.setState({ value: '' })
    else this.setState({ value })
  }

  render() {
    let { className, tabIndex, disabled, timeFormat, style } = this.props;
    let classes = className ? `form-control ${className}` : 'form-control';
    let { value } = this.state;

    const displayValue = value || (timeFormat ? '__/__/____ __:__' : '__/__/____');

    return (
      <input ref={this.inputRef}
        className={classes} value={displayValue} tabIndex={tabIndex} disabled={disabled} style={style}
        onChange={(e) => this.onAccept(e.target.value)} onBlur={this.onFocusLost} />
    );
  }

}

export interface WTimeInputMaskProps extends ELEProps {
  name: string;
  value: string;
  tabIndex?: number;
  disabled?: boolean;
  onChange?: (value: moment.Moment | null) => void;
  inputObserver?: InputObserver;
}

interface WTimeInputMaskState {
  initialValue: string;
  value: string;
}

export class WTimeInputMask extends Component<WTimeInputMaskProps, WTimeInputMaskState> {
  inputRef: React.RefObject<HTMLInputElement>;
  mask: InputMask<any> | null = null;

  constructor(props: WTimeInputMaskProps) {
    super(props);
    this.state = { initialValue: props.value, value: props.value };
    this.inputRef = React.createRef();
  }

  componentDidMount() {
    const maskPattern = '00:00';

    if (this.inputRef.current) {
      const maskOptions: FactoryArg = {
        mask: maskPattern,
        lazy: false,
        overwrite: true,
      }
      this.mask = IMask(this.inputRef.current, maskOptions);
    }
  }

  componentWillUnmount() {
    if (this.mask) {
      this.mask.destroy();
    }
  }

  onFocusLost = (evt: any) => {
    let { onChange, inputObserver, name } = this.props;
    let value = evt.target.value;

    if (value.trim() === '' || value === '__:__') {
      this.setState({ value: '' });
      if (inputObserver) {
        inputObserver.getInputChangeCollector().addChange(name, this.state.initialValue, '');
      }
      if (onChange) onChange(null);
      return;
    }

    // Auto-complete when only hours are entered (e.g. "9" or "10")
    if (value.match(/^\d{1,2}$/)) {
      const hours = parseInt(value);
      if (hours >= 0 && hours <= 23) {
        value = hours.toString().padStart(2, '0') + ':00';
      }
    }

    let momentTime = moment(value, 'HH:mm');

    if (!momentTime.isValid()) {
      this.setState({ value: this.state.initialValue });
      return;
    }

    this.setState({ value: momentTime.format('HH:mm') });

    if (inputObserver) {
      inputObserver.getInputChangeCollector().addChange(name, this.state.initialValue, value);
    }
    if (onChange) onChange(momentTime);
  }

  onAccept = (value: string) => {
    if (value.includes('_')) this.setState({ value: '' })
    else this.setState({ value })
  }

  render() {
    let { className, tabIndex, disabled } = this.props;
    let classes = className ? `form-control ${className}` : 'form-control';
    let { value } = this.state;

    const displayValue = value || '__:__';

    return (
      <input ref={this.inputRef}
        className={classes} value={displayValue} tabIndex={tabIndex} disabled={disabled}
        onChange={(e) => this.onAccept(e.target.value)} onBlur={this.onFocusLost} />
    );
  }
}

export interface WSliderProps extends ELEProps {
  min: number;
  max: number;
  step: number;
  name: string;
  value: number;
  onChange?: (value: number) => void;
  inputObserver?: InputObserver;
}
interface WSliderState {
  initialValue: number;
  value: number;
}
export class WSlider extends Component<WSliderProps, WSliderState> {
  static getDerivedStateFromProps(props: WSliderProps, state: WSliderState) {
    let { value } = props;
    if (state.initialValue != value) {
      let newState: WSliderState = { initialValue: value, value: value };
      return newState;
    }
    return state;
  }

  constructor(props: WSliderProps) {
    super(props);
    let { value } = props;
    this.state = { initialValue: value, value: value };
  }

  handleChange = (event: any) => {
    let { onChange, inputObserver, name } = this.props;
    let value = event.target.value;
    this.setState({ value: value });
    if (inputObserver) {
      inputObserver.getInputChangeCollector().addChange(name, null, value);
    }
    if (onChange) onChange(value);
  }

  render() {
    let { name, min, max, step, className } = this.props;
    let classes = className ? `form-control ${className}` : 'form-control';
    let html = (
      <input className={classes}
        name={name} type="range" value={this.state.value} min={min} max={max} step={step}
        onChange={this.handleChange} />
    );
    return html;
  }
}