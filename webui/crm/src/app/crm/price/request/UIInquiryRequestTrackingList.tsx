import React, { createRef, RefObject } from 'react';
import * as FeatherIcon from 'react-feather';
import { util, grid, entity, bs, app, server, input } from '@datatp-ui/lib';
import { module } from '@datatp-ui/erp';

import { GroupType, PricingRequestStatusUtils, T } from '../backend';

import { UIInquiryRequestReportPlugin } from './UIInquiryRequestList';
import { JOB_TRACKING_CONFIG, SectionConfig, StepConfig } from './JobTrackingConfig';
import { UICheckRequestListEditor } from './UIInquiryRequestListEditor';
import { UIUploadPriceButton } from '../common/UIUploadFile';
import { buildTooltipValues } from '../common';
import { UIInquiryDashboard } from './UIPricingDashboard';

import settings = module.settings;
import BBRefLocation = settings.BBRefLocation;
import LocationType = settings.LocationType;
import { mapToTypeOfShipment, TransportationMode, TransportationTool } from 'app/crm/common';

const SESSION = app.host.DATATP_SESSION;

interface UIJobTrackingForImportProps extends app.AppComponentProps {
  request: any;
  onPostCommit: (request: any, updateInList: boolean) => void;
}
export class UIJobTrackingForImport extends app.AppComponent<UIJobTrackingForImportProps> {
  viewId: number = util.IDTracker.next();
  currentKeyTrackerMap: Record<string, any> = {};

  saveChanges = (modified: any) => {
    let { appContext, onPostCommit } = this.props;
    if (!modified['pricingAccountId']) {
      modified['pricingAccountId'] = SESSION.getAccountId();
      modified['pricingLabel'] = SESSION.getAccountAcl().getFullName();
      modified['pricingDate'] = util.TimeUtil.javaCompactDateTimeFormat(new Date())
    }

    let jobTrackingStatus = modified['jobTrackingStatus'] || {};

    let totalStepDoneCount = 0;
    JOB_TRACKING_CONFIG.forEach((section) => {
      section.steps.forEach((step) => {
        if (step.id !== '2.1' && step.id !== '2.2' && jobTrackingStatus[step.id] === 'Done') {
          totalStepDoneCount++;
        }
      });
    });

    totalStepDoneCount += (jobTrackingStatus['totalNewPricesCount'] || 0);
    totalStepDoneCount += (jobTrackingStatus['totalAnalysisPricesCount'] || 0);

    const findLastStepName = (): string => {
      for (let i = JOB_TRACKING_CONFIG.length - 1; i >= 0; i--) {
        const section = JOB_TRACKING_CONFIG[i];
        for (let j = section.steps.length - 1; j >= 0; j--) {
          const step = section.steps[j];
          if (jobTrackingStatus[step.id] && jobTrackingStatus[step.id] !== 'NA') {
            return `${step.id} ${step.label}`;
          }
        }
      }
      return '';
    }

    let updateRequest: any = {
      ...modified,
      jobTrackingStatus,
      "lastStepName": findLastStepName(),
      "stepDoneCount": totalStepDoneCount,
    }
    onPostCommit(updateRequest, true);

    appContext.createHttpBackendCall('TransportPriceMiscService', 'saveInquiryRequestRecords', { records: [updateRequest] })
      .withSuccessData((_data: any) => {
        appContext.addOSNotification("success", T(`Auto save modified records success!!!`));
        // this.vgridContext.getVGrid().forceUpdateView();
      })
      .call();
  }

  renderSectionStep = (jobTracking: any, section: SectionConfig, step: StepConfig, index: number): JSX.Element => {
    const { request } = this.props;

    return (
      <div className='flex-hbox align-items-center py-1' key={step.id}>
        <div className={`text-muted me-2`}>{`${step.id}`}</div>
        <input.BBRadioInputField bean={jobTracking} field={step.id}
          options={['NA', 'Issue', 'Done']}
          optionLabels={['N/A', 'Issue', 'Done']}

          onInputChange={(_bean: any, _field: string, _oldVal: any, newVal: any) => {
            jobTracking[step.id] = newVal;
            let indexOfSection: number = JOB_TRACKING_CONFIG.indexOf(section);

            if (index > 0) {
              // cap nhat cac step truoc do cua section hien tai
              for (let k = 0; k < index; k++) { // index của step trong secsion hien tai.
                const stepId = section.steps[k].id;
                if (!jobTracking[stepId] || jobTracking[stepId] === 'NA') {
                  jobTracking[stepId] = newVal;
                }
              }
            }

            if (indexOfSection > 0) {
              // cap nhat cac step truoc do cua secsion truoc section hien tai
              for (let i = 0; i < indexOfSection; i++) {
                let currentSection: SectionConfig = JOB_TRACKING_CONFIG[i];
                for (let j = 0; j < currentSection.steps.length; j++) {
                  const stepId = currentSection.steps[j].id;
                  if (!jobTracking[stepId] || jobTracking[stepId] === 'NA') {
                    jobTracking[stepId] = newVal;
                  }
                }
              }
            }
            request['jobTrackingStatus'] = jobTracking;
            this.currentKeyTrackerMap[section.title] = util.IDTracker.next();
            this.forceUpdate();
            this.saveChanges(request)
          }} />
      </div>
    )

  }

  componentDidUpdate(prevProps: Readonly<UIJobTrackingForImportProps>, prevState: Readonly<any>, snapshot?: any): void {
    const { request } = this.props;
    if (request['id'] !== prevProps.request['id']) {
      this.viewId = util.IDTracker.next();
      this.forceUpdate();
    }
  }

  onResetUpdatePrice = (jobTracking: any) => {
    const { request } = this.props;
    request['totalNewPricesCount'] = 0;
    jobTracking['2.1'] = 'Done';
    request['jobTrackingStatus'] = jobTracking;
    this.currentKeyTrackerMap['2. UPDATE GIÁ BẢNG (FCL/LCL/AIR)'] = util.IDTracker.next();
    this.forceUpdate();
    this.saveChanges(request);
  }


  render(): React.ReactNode {
    const { request, appContext, pageContext } = this.props;
    let section_1: SectionConfig = JOB_TRACKING_CONFIG[0];
    let section_2: SectionConfig = JOB_TRACKING_CONFIG[1];
    let section_3: SectionConfig = JOB_TRACKING_CONFIG[2];

    let section_4: SectionConfig = JOB_TRACKING_CONFIG[3];
    let section_5: SectionConfig = JOB_TRACKING_CONFIG[4];
    let section_6: SectionConfig = JOB_TRACKING_CONFIG[5];


    let mode: TransportationMode = request['mode'];
    let purpose = request['purpose']
    let label = mapToTypeOfShipment(purpose, mode);

    let jobTracking: any = typeof request['jobTrackingStatus'] === 'string'
      ? JSON.parse(request['jobTrackingStatus'])
      : (request['jobTrackingStatus'] || {});

    // Initialize missing steps with 'NA'
    JOB_TRACKING_CONFIG.forEach(section => {
      section.steps.forEach(step => {
        if (!jobTracking[step.id]) {
          jobTracking[step.id] = 'NA';
        }
      });
    });

    return (
      <div className='flex-hbox flex-grow-0 d-flex gap-1' key={this.viewId}
        style={{
          height: 250,
          overflowX: 'auto',
          overflowY: 'hidden',
          whiteSpace: 'nowrap'
        }}>

        <div className='flex-vbox flex-grow-0 border px-2 py-2 m-1 shadow-sm rounded bg-white'
          style={{ minWidth: '300px', flex: '0 0 auto' }}
          key={this.currentKeyTrackerMap[section_1.title] || section_1.title}>

          <div className='flex-grow-0 border-bottom mb-1 d-flex justify-content-between align-items-center'>
            <h6 className='py-1 mb-0 fs-9'>{section_1.title}</h6>
          </div>

          <div className='flex-grow-1 px-2 py-2'>
            {section_1.steps.map((step: StepConfig, index: number) => {
              return this.renderSectionStep(jobTracking, section_1, step, index)
            })}
          </div>
        </div>

        <div className='flex-vbox flex-grow-0 border px-2 py-2 m-1 shadow-sm rounded bg-white'
          style={{ minWidth: '300px', flex: '0 0 auto' }}
          key={this.currentKeyTrackerMap[section_2.title] || section_2.title}>
          <div className='flex-grow-0 border-bottom mb-1'>
            <h6 className='py-1'>{"2. UPDATE GIÁ BẢNG (FCL/LCL/AIR)"}</h6>
          </div>

          <div className='flex-vbox flex-grow-0 gap-2 '>

            <div className='flex-vbox border-end me-2 pe-2' key={'2.1'}>
              <div className={`text-muted`}> {`2.1 Update giá bảng (${label})`} </div>
              <div className='flex-hbox'>
                <input.BBNumberField bean={request} field={'totalNewPricesCount'} disable />

                <UIUploadPriceButton appContext={appContext} pageContext={pageContext}
                  className='d-flex align-items-center ms-1 px-2 py-1 flex-grow-0 flex-shrink-0 rounded-1'
                  mode={mode} purpose={purpose} onSuccess={(_records: any[], _groupType?: GroupType) => {
                    request['totalNewPricesCount'] = _records.length;
                    jobTracking['2.1'] = 'Done';
                    request['jobTrackingStatus'] = jobTracking;
                    this.currentKeyTrackerMap[section_2.title] = util.IDTracker.next();
                    this.forceUpdate();
                    this.saveChanges(request)
                  }} />

                <bs.Button laf="warning" outline
                  className="d-flex align-items-center ms-1 px-2 py-1 flex-grow-0 flex-shrink-0 rounded-1"
                  onClick={() => this.onResetUpdatePrice(jobTracking)}>
                  <FeatherIcon.Upload size={14} className="me-1" />
                  <span className="fw-semibold">Reset</span>
                </bs.Button>

              </div>
            </div>

            <div className='flex-vbox' key={'2.2'}>
              <div className={`text-muted`}>{`2.2 Phân tích giá (${label})`}</div>
              <input.BBNumberField bean={request} field={'totalAnalysisPricesCount'}
                onInputChange={(bean: any, _field: string, _oldVal: any, newVal: any) => {
                  bean[_field] = newVal;
                  jobTracking['2.2'] = 'Done';
                  this.currentKeyTrackerMap[section_2.title] = util.IDTracker.next();
                  this.forceUpdate();
                  this.saveChanges(request)
                }} />
            </div>
          </div>
        </div>


        {[section_3, section_4, section_5, section_6].map((section: SectionConfig) => {
          return (
            <div className='flex-vbox flex-grow-0 border px-2 py-2 m-1 shadow-sm rounded bg-white'
              style={{ minWidth: '300px', flex: '0 0 auto' }}
              key={this.currentKeyTrackerMap[section.title] || section.title}>

              <div className='flex-grow-0 border-bottom mb-1 d-flex justify-content-between align-items-center'>
                <h6 className='py-1 mb-0 fs-9'>{section.title}</h6>
              </div>

              <div className='flex-grow-1 px-2 py-2'>
                {section.steps.map((step: StepConfig, index: number) => {
                  return this.renderSectionStep(jobTracking, section, step, index)
                })}
              </div>
            </div>
          )
        })}
      </div>
    )
  }
}

export interface UIInquiryRequestTrackingListProps extends entity.VGridEntityListEditorProps {
  enableJobStep: boolean;
}
export class UIInquiryRequestTrackingList extends entity.VGridEntityListEditor<UIInquiryRequestTrackingListProps> {
  viewId = util.IDTracker.next();

  createVGridConfig(): grid.VGridConfig {
    const { appContext, pageContext, readOnly, enableJobStep } = this.props;
    let moderatorCap = pageContext.hasUserModeratorCapability();
    let writeCap = pageContext.hasUserWriteCapability() && !readOnly

    const handleClick = (text: string) => {
      navigator.clipboard.writeText(text);
      bs.toastShow('Copied to clipboard!', { type: 'success' });
    };

    const tooltipFields = [
      { key: 'note', label: 'Note' },
      { key: 'pickupAddress', label: 'Pickup Address' },
      { key: 'deliveryAddress', label: 'Delivery Address' },
      { key: 'descOfGoods', label: 'Desc Of Goods' },
      { key: 'mailTo', label: 'Mail To' },
      { key: 'mailCc', label: 'Mail Cc' },
      { key: 'mailSubject', label: 'Mail Subject' },
      { key: 'pricingNote', label: 'Pricing Note' },
    ];

    const renderTooltipAdvanced = (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
      const record = dRecord.record;
      const val = record[field.name] || 'N/A';

      let mailTo = (record['mailTo'] || '').split(",").map((item: string) => item.trim()).join("\n");
      record['mailTo'] = mailTo

      const { htmlFormat, textFormat } = buildTooltipValues(record, tooltipFields)

      return (
        <bs.CssTooltip width={400} position='bottom-right' offset={{ x: 380, y: 0 }}>
          <bs.CssTooltipToggle>
            <div className="flex-hbox" style={{ cursor: 'pointer', userSelect: 'text' }}
              onClick={() => {
                navigator.clipboard.writeText(textFormat);
                bs.toastShow('Copied to clipboard!', { type: 'success' });
                this.onSelect(dRecord.row, dRecord.record)
              }}>
              {field.fieldDataGetter ? field.fieldDataGetter(record) : field.format ? field.format(val) : val}
            </div>
          </bs.CssTooltipToggle>
          <bs.CssTooltipContent>
            {htmlFormat}
          </bs.CssTooltipContent>
        </bs.CssTooltip >
      );
    }

    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: 40,
        editor: {
          supportViewMode: ['table'],
          enable: (writeCap) || moderatorCap,
        },
        fields: [
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'code', label: T(`Ref`), width: 120, container: 'fixed-left', filterable: true,
            computeCssClasses: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
              if (dRecord.getRecordState().isMarkModified()) return 'fw-bold cell-selected'
              return ''
            },
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let val = dRecord.record[_field.name] || '';
              return <div className="flex-hbox" style={{ cursor: 'pointer', userSelect: 'text' }}
                onClick={() => {
                  handleClick(val)
                  this.onSelect(dRecord.row, dRecord.record)
                }}>{val}</div>
            }
          },
          {
            name: "salemanLabel", label: 'Saleman.', width: 180, filterable: true,
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let uiList = ctx.uiRoot as entity.DbEntityList
              const { appContext, pageContext } = uiList.props;

              let employeeName: string = record['salemanLabel'] || 'N/A';

              let parts = employeeName.trim().toLowerCase().split(' ');
              parts = parts.map(word => word.charAt(0).toUpperCase() + word.slice(1));
              if (parts.length > 3) {
                let initials = parts[0][0] + '.' + parts[1][0] + '.';
                employeeName = `${initials} ${parts.slice(2).join(' ')}`;
              }

              let mailTo = (record['mailTo'] || '').split(",").map((item: string) => item.trim()).join("\n");
              record['mailTo'] = mailTo

              const { htmlFormat, textFormat } = buildTooltipValues(record, tooltipFields)

              return (
                <bs.CssTooltip width={400} position='bottom-right' offset={{ x: 380, y: 0 }}>
                  <bs.CssTooltipToggle>
                    <div className='flex-hbox' onClick={() => {
                      navigator.clipboard.writeText(textFormat);
                      bs.toastShow('Copied to clipboard!', { type: 'success' });
                      this.onSelect(dRecord.row, dRecord.record)
                    }}>
                      <div className='flex-hbox justify-content-center align-items-center' style={{ cursor: 'pointer', userSelect: 'text' }}
                        onClick={() => this.onSelect(dRecord.row, dRecord.record)}>
                        <module.account.WAvatars className='px-2'
                          appContext={appContext} pageContext={pageContext} avatarIds={[record['salemanAccountId']]}
                          avatarIdType='AccountId' width={20} height={20} borderRadius={10} />
                        <div className="flex-hbox">{employeeName}</div>
                      </div>
                    </div>
                  </bs.CssTooltipToggle>
                  <bs.CssTooltipContent>
                    {htmlFormat}
                  </bs.CssTooltipContent>
                </bs.CssTooltip>

              )
            },
          },
          {
            name: 'typeOfShipment', label: T(`Type of shpt`), width: 120, filterable: true, filterableType: 'string',
            fieldDataGetter(record: any) {
              return `${mapToTypeOfShipment(record['purpose'], record['mode'])}`
            },
            customRender: renderTooltipAdvanced
          },
          {
            name: 'requestDate', label: T(`Req. Date`), width: 130, format: util.text.formater.compactDate,
            filterable: true, filterableType: 'date', sortable: true,
            fieldDataGetter(record: any) {
              let val: string = record['requestDate']
              return `${val ? util.text.formater.compactDate(val) : ''}`
            },
            customRender: renderTooltipAdvanced
          },
          {
            name: 'termOfService', label: T(`Term.`), width: 90, filterable: true, filterableType: 'string',
            customRender: renderTooltipAdvanced
          },
          {
            name: 'fromLocationLabel', label: T('From Location'), width: 200, filterable: true, filterableType: 'string',
            editor: {
              type: "string", enable: enableJobStep, onInputChange: (ctx: grid.FieldContext, _oldVal: any, _newVal: any) => {
                let record: any = ctx.displayRecord.record;
                this.saveChanges(record)
              },
              renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                const { fieldConfig, displayRecord, tabIndex, focus } = ctx;
                let record: any = displayRecord.record;
                let types: LocationType[] = ['Port', 'Airport', 'State']
                if (TransportationTool.isAir(record['mode'])) types = ['Airport']
                if (TransportationTool.isSea(record['mode'])) types = ['Port']
                return (
                  <BBRefLocation style={{ minHeight: 40 }} minWidth={400}
                    placeholder={'Port of Loading'} autofocus={focus} appContext={appContext} pageContext={pageContext}
                    hideMoreInfo bean={record} beanIdField={'fromLocationCode'} beanLabelField={'fromLocationLabel'}
                    tabIndex={tabIndex} locationTypes={types} refLocationBy='code'
                    onPostUpdate={(_inputUI, bean, _selectOpt, _userInput) => {
                      bean['fromLocationCode'] = _selectOpt['code']
                      bean['fromLocationLabel'] = _selectOpt['label']
                      onInputChange(bean, fieldConfig.name, null, bean[fieldConfig.name])
                    }} />
                )
              }
            }
          },
          {
            name: 'toLocationLabel', label: T('To Location'), width: 200, filterable: true, filterableType: 'string',
            editor: {
              type: "string", enable: enableJobStep, onInputChange: (ctx: grid.FieldContext, oldVal: any, newVal: any) => {
                let record: any = ctx.displayRecord.record;
                this.saveChanges(record)
              },
              renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                const { fieldConfig, displayRecord, tabIndex, focus } = ctx;
                let record: any = displayRecord.record;
                let types: LocationType[] = ['Port', 'Airport', 'State']
                if (TransportationTool.isAir(record['mode'])) types = ['Airport']
                if (TransportationTool.isSea(record['mode'])) types = ['Port']
                return (
                  <BBRefLocation style={{ minHeight: 40 }} minWidth={400}
                    placeholder={'Port of Loading'} autofocus={focus} appContext={appContext} pageContext={pageContext}
                    hideMoreInfo bean={record} beanIdField={'toLocationCode'} beanLabelField={'toLocationLabel'}
                    tabIndex={tabIndex} locationTypes={types} refLocationBy='code'
                    onPostUpdate={(_inputUI, bean, _selectOpt, _userInput) => {
                      bean['toLocationCode'] = _selectOpt['code']
                      bean['toLocationLabel'] = _selectOpt['label']
                      onInputChange(bean, fieldConfig.name, null, bean[fieldConfig.name])
                    }} />
                )
              }
            }
          },
          {
            name: 'cargoReadyDate', label: T(`ETD/ CRD`), width: 180,
            fieldDataGetter(record: any) {
              let val: string = record['cargoReadyDate']
              return `${val ? util.text.formater.compactDate(val) : ''}`
            },
            customRender: renderTooltipAdvanced
          },
          {
            name: 'volumeInfo', label: T(`Vol`), width: 120,
            fieldDataGetter(record: any) {
              let val: string = record['volumeInfo']
              if (val) return val;
              let mode: TransportationMode = record['mode']
              if (TransportationTool.isAir(mode)) return record['grossWeightKg'] + ' KGS'
              if (TransportationTool.isSeaLCL(mode)) return record['volumeCbm'] + ' CBM'
            },
            customRender: renderTooltipAdvanced
          },
          {
            name: 'reportVolume', label: T(`Total Vol`), width: 120,
            fieldDataGetter(record: any) {
              let val: string = record['reportVolume']
              if (val) val += ` (${record['reportVolumeUnit']})`
              return val;
            },
            customRender: renderTooltipAdvanced
          },
          {
            name: 'feedback', label: T(`Feedback`), width: 270, style: { height: 40 },
            editor: {
              type: 'string',
              enable: (writeCap) || moderatorCap,
              onInputChange: (ctx: grid.FieldContext, oldVal: any, newVal: any) => {
                let record: any = ctx.displayRecord.record;
                if (newVal !== oldVal) {
                  record['feedback'] = newVal;
                  const { fieldConfig } = ctx;
                  let event: grid.VGridCellEvent = {
                    row: record.row,
                    field: fieldConfig,
                    event: 'Modified'
                  }
                  this.vgridContext.broadcastCellEvent(event);
                  this.saveChanges(record)
                }
              },
            }
          },

          {
            name: 'pricingNote', label: T(`Pricing Note`), width: 270, style: { height: 40 },
            editor: {
              type: 'string',
              enable: (writeCap) || moderatorCap,
              onInputChange: (ctx: grid.FieldContext, oldVal: any, newVal: any) => {
                let record: any = ctx.displayRecord.record;
                if (newVal !== oldVal) {
                  record['pricingNote'] = newVal;
                  this.saveChanges(record)
                  const { fieldConfig } = ctx;
                  let event: grid.VGridCellEvent = {
                    row: record.row,
                    field: fieldConfig,
                    event: 'Modified'
                  }
                  this.vgridContext.broadcastCellEvent(event);
                }
              },
            }
          },
          {
            name: "pricingLabel", label: 'Pricing By.', width: 130, filterable: true,
            fieldDataGetter(record) {
              return record['pricingLabel'] || 'N/A';
            },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                let _rowRecord = cell.getDisplayRecord().record;
                let fieldName: string = event.field.name;
                if (fieldName === 'pricingNote' || fieldName === 'feedback' || fieldName === 'status') {
                  cell.forceUpdate()
                }
              },
            },
            customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let employeeName: string = record['pricingLabel'] || 'N/A';
              let parts = employeeName.trim().toLowerCase().split(' ');
              parts = parts.map(word => word.charAt(0).toUpperCase() + word.slice(1));
              if (parts.length >= 3) {
                let initials = parts[0][0] + '.' + parts[1][0] + '.';
                employeeName = `${initials} ${parts.slice(2).join(' ')}`;
              }
              return (
                <div className="flex-hbox">{employeeName}</div>
              )
            },
          },
          {
            name: 'pricingDate', label: T(`Pricing Date`), width: 120,
            format: util.text.formater.compactDate, filterable: true, filterableType: 'date',
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                let _rowRecord = cell.getDisplayRecord().record;
                let fieldName: string = event.field.name;
                if (fieldName === 'pricingNote' || fieldName === 'feedback' || fieldName === 'status') {
                  cell.forceUpdate()
                }
              },
            },
          },
          {
            name: 'mailSubject', label: T(`Subject`), width: 340, style: { height: 40 },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              let valTruncate = util.text.formater.uiTruncate(dRec.record[field.name], 340, true);
              return <div className="flex-hbox">{valTruncate}</div>
            }
          },
          {
            name: 'status', label: T('Status'), width: 120,
            filterable: true, container: 'fixed-right',
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                let _rowRecord = cell.getDisplayRecord().record;
                let fieldName: string = event.field.name;
                if (fieldName === 'pricingNote' || fieldName === 'feedback' || fieldName === 'status') {
                  cell.forceUpdate()
                }
              },
            },
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let currentStatus = PricingRequestStatusUtils.getStatusInfo(record['status']);
              let StatusIcon = currentStatus.icon;
              let label = currentStatus.label;
              let color = currentStatus.color;

              const statusList = PricingRequestStatusUtils.getPricingRequestStatusList();
              const statusRemaining = statusList.filter(status =>
                status.value !== record['status'] &&
                status.value !== 'IN_PROGRESS'
              );

              return (
                <bs.Popover className="d-flex flex-center w-100 h-100"
                  title={T('Status')} closeOnTrigger=".btn" >
                  <bs.PopoverToggle
                    className={`flex-hbox flex-center px-2 py-2 rounded-2 bg-${color}-subtle text-${color} w-100 `}>
                    <StatusIcon size={14} className="me-1" />
                    <span>{label}</span>
                  </bs.PopoverToggle>
                  <bs.PopoverContent>
                    <div className='flex-vbox gap-2' style={{ width: '200px' }}>
                      {statusRemaining.map(opt => {
                        let OptIcon = opt.icon;
                        return (
                          <div key={opt.value}
                            className={`d-flex flex-center px-2 py-1 rounded-2 bg-${opt.color}-subtle text-${opt.color} w-100 cursor-pointer`}
                            onClick={() => {

                              if (opt.value !== 'SUCCESS' && opt.value !== 'NO_RESPONSE' && opt.value !== 'DONE' && !record.feedback) {
                                bs.dialogShow('Warning',
                                  <div className="text-warning fw-bold text-center py-3 border-bottom">
                                    <FeatherIcon.AlertCircle className="mx-2" />
                                    Please provide feedback before changing status
                                  </div>,
                                  { backdrop: 'static', size: 'md' }
                                );
                                this.vgridContext.getVGrid().forceUpdateView();
                                return;
                              } else {
                                record['status'] = opt.value;
                                this.saveChanges(record)
                                let event: grid.VGridCellEvent = {
                                  row: record.row,
                                  field: _field,
                                  event: 'Modified'
                                }
                                this.vgridContext.broadcastCellEvent(event);
                              }
                            }
                            }>
                            <OptIcon size={14} className="me-1" />
                            <span>{opt.label}</span>
                          </div>
                        );
                      })}
                    </div>
                  </bs.PopoverContent>
                </bs.Popover>
              );
            }
          },
        ],
      },
      toolbar: { hide: true },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          }
        }
      },
    }

    let fields: grid.FieldConfig[] = config.record.fields || [];

    const totalWidth = window.innerWidth || 1920;
    const totalControlWidth = config.record.control ? config.record.control.width : 0;
    let totalWidthRemaining = totalWidth - totalControlWidth - fields.reduce((sum, field) => {
      const isVisible =
        !field.state ||
        !field.state.hasOwnProperty('visible') ||
        field.state.visible === true;
      return isVisible ? sum + (field.width || 0) : sum;
    }, 0);

    if (totalWidthRemaining > 0) {
      fields[fields.length - 1].container ? fields[fields.length - 1].container = 'default' : null;
    }

    return config;
  }

  computeToSelectFirstRow() {
    let records = this.vgridContext.model.getRecords();
    if (records.length > 0) {
      this.onSelect(0, records[0]);
    }
    this.vgridContext.getVGrid().forceUpdateView();
  }

  componentDidMount(): void {
    this.computeToSelectFirstRow();
  }

  onSelect(row: number, record: any) {
    const { appContext, pageContext, onSelect } = this.props;
    const dRecords = this.vgridContext.model.getDisplayRecordList().getDisplayRecords();
    for (let dRec of dRecords) {
      if (dRec.record['id'] === this.getSelectBean().bean['id']) {
        dRec.record = this.getSelectBean().bean
      }
      grid.initRecordState(dRec.record, dRec.row);
    }
    let state = grid.getRecordState(record);
    if (!state) state = grid.initRecordState(record, row);
    state.selected = true;
    // this.vgridContext.getVGrid().forceUpdateView();
    this.selectBean.bean = record;
    this.nextEditorViewId();
    if (onSelect) onSelect(appContext, pageContext, record);
    else {
      this.forceUpdate();
    }
  }

  override renderBeanEditor() {
    return <div>TODO</div>
  }

  saveChanges = (modified: any) => {
    let { appContext } = this.props;
    if (!modified['pricingAccountId']) {
      modified['pricingAccountId'] = SESSION.getAccountId();
      modified['pricingLabel'] = SESSION.getAccountAcl().getFullName();
      modified['pricingDate'] = util.TimeUtil.javaCompactDateTimeFormat(new Date())
    }
    this.vgridContext.model.addOrUpdateByRecordId(modified);

    appContext.createHttpBackendCall('TransportPriceMiscService', 'saveInquiryRequestRecords', { records: [modified] })
      .withSuccessData((_data: any) => {
        appContext.addOSNotification("success", T(`Auto save modified records success!!!`));
        // this.vgridContext.getVGrid().forceUpdateView();
      })
      .call();
  }

  renderJobTracking() {
    const { appContext, pageContext, enableJobStep } = this.props;
    if (!enableJobStep) return <></>;
    return (
      <UIJobTrackingForImport
        appContext={appContext} pageContext={pageContext} request={this.getSelectBean().bean}
        onPostCommit={(request, updateInList) => {
          if (updateInList) {
            this.vgridContext.model.addOrUpdateByRecordId(request);
            this.vgridContext.getVGrid().forceUpdateView();
          }
        }} />
    )
  }

  render(): React.JSX.Element {

    return (
      <div className='flex-vbox' key={this.viewId}>
        <div className="flex-vbox bg-white">
          <grid.VGrid context={this.vgridContext} />
        </div>
        <div key={this.editorViewId}>
          {this.renderJobTracking()}
        </div>
      </div>
    )
  }

}

export class UIInquiryRequestTrackingListPage extends app.AppComponent {
  viewId: number = util.IDTracker.next();
  plugin: UIInquiryRequestReportPlugin;
  records: Array<any> = [];
  filter: any = { maxReturn: 500, enableJobStep: false, pattern: '' };
  vgridRef: RefObject<UIInquiryRequestTrackingList>;

  constructor(props: app.AppComponentProps) {
    super(props);
    this.filter.enableJobStep = localStorage.getItem('enableJobStep') === 'true';
    this.plugin = new UIInquiryRequestReportPlugin('System').withRequestFilterMode('priority');
    this.vgridRef = createRef<UIInquiryRequestTrackingList>();
    this.loadData();
  }

  loadData(): void {
    this.markLoading(true);
    const { appContext } = this.props;
    let backend = this.plugin.backend;
    if (!backend || !backend.searchMethod) throw new Error("Need to config backend");

    const callback = (response: server.BackendResponse) => {
      this.records = response.data;

      for (let request of this.records) {
        let jobTracking: any = typeof request['jobTrackingStatus'] === 'string'
          ? JSON.parse(request['jobTrackingStatus'])
          : (request['jobTrackingStatus'] || {});

        request['jobTrackingStatus'] = jobTracking;
      }

      // Sort records: IN_PROGRESS first, then by requestDate descending
      this.records.sort((a, b) => {
        // First priority: IN_PROGRESS status
        if (a.status === 'IN_PROGRESS' && b.status !== 'IN_PROGRESS') return -1;
        if (a.status !== 'IN_PROGRESS' && b.status === 'IN_PROGRESS') return 1;

        // Second priority: requestDate descending (newer dates first)
        const dateA = util.TimeUtil.parseCompactDateTimeFormat(a.requestDate);
        const dateB = util.TimeUtil.parseCompactDateTimeFormat(b.requestDate);

        if (!dateA && !dateB) return 0;
        if (!dateA) return 1;
        if (!dateB) return -1;

        return dateB.getTime() - dateA.getTime();
      });

      this.markLoading(false);
      this.viewId = util.IDTracker.next();
      this.forceUpdate();

      if (this.vgridRef?.current) {
        this.vgridRef.current.forceUpdate();
      }

    }
    appContext.createHttpBackendCall(backend.service, backend.searchMethod, { sqlParams: this.plugin.getSearchParams() })
      .withSuccess(callback).call();
  }

  onModify = (_bean: any, _field: string, _oldVal: any, _newVal: any): void => {
    if (_field === 'enableJobStep') {
      this.filter.enableJobStep = _newVal;
      this.viewId = util.IDTracker.next();
      localStorage.setItem('enableJobStep', _newVal);
    }
    this.forceUpdate();
  };

  onChangePattern = (_oldVal: any, newVal: any) => {
    if (_oldVal !== newVal) {
      this.filter.pattern = newVal;
      this.plugin = new UIInquiryRequestReportPlugin('System')
      if (newVal) {
        if (newVal.length > 3) this.plugin.withSearchPattern(`*${newVal}*`)
      } else {
        this.plugin.withRequestFilterMode('priority');
      }
      this.loadData();
      // this.viewId = util.IDTracker.next();
      // this.forceUpdate();
    }
  }

  onReportView = () => {
    const { pageContext } = this.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UIInquiryDashboard appContext={appCtx} pageContext={pageCtx} space='User' />
      )
    }
    let popupId = `inquiry-request-report-${util.IDTracker.next()}`;
    let popupLabel = `Inquiry Request Report (${SESSION.getAccountAcl().getFullName()})`;
    pageContext.createPopupPage(popupId, popupLabel, createAppPage, { size: 'xl', backdrop: 'static' });
  }

  onBulkInput = () => {
    const { pageContext } = this.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {

      const onPostCommit = (_entity: any, uiEditor?: app.AppComponent) => {
        //uiEditor?.props.pageContext.back();
        pageCtx.back();
        this.viewId = util.IDTracker.next();
        this.forceUpdate();
      }

      return (
        <UICheckRequestListEditor appContext={appCtx} pageContext={pageCtx} plugin={new entity.DbEntityListPlugin([])}
          onPostCommit={onPostCommit} />
      )
    }
    let popupId = `bulk-input-${util.IDTracker.next()}`;
    pageContext.createPopupPage(popupId, "Bulk Input Inquiry", createAppPage, { size: 'xl', backdrop: 'static' });
  }

  render(): React.ReactNode {
    if (this.isLoading()) return this.renderLoading();

    const { appContext, pageContext } = this.props;

    return (
      <div className='flex-vbox'>

        <div className='bg-white flex-hbox flex-grow-0 justify-content-between align-items-center mx-1 px-1'>

          <div className='flex-hbox'>
            <div className='me-2'>
              <input.BBSelectField className="fw-bold text-primary border-bottom text-center"
                style={{ width: 150 }} bean={this.filter} field={"maxReturn"}
                options={[500, 1000, 2000, 5000]}
                optionLabels={['Show 500 records', 'Show 1000 records', 'Show 2000 records', 'Show 5000 records']}
                onInputChange={this.onModify} />
            </div>

            <input.WStringInput className={'flex-hbox'} style={{ maxWidth: 300 }}
              name='search' value={this.filter.pattern}
              placeholder={('Enter Ref or Subject...')} onChange={this.onChangePattern} />

          </div>

          <div className="flex-hbox flex-grow-0 justify-content-end align-items-center">

            <bs.Button laf='info' className="border-0 rounded-0 p-1 mx-1" outline
              onClick={this.onReportView} hidden={bs.ScreenUtil.isMobileScreen()}>
              <FeatherIcon.BarChart2 size={12} /> Reports
            </bs.Button>

            <bs.Button laf='info' className="border-0 border-end rounded-0 p-1 mx-1" outline
              onClick={this.onBulkInput} hidden={bs.ScreenUtil.isMobileScreen()}>
              <FeatherIcon.Plus size={12} /> Bulk Input
            </bs.Button>

            <div className="form-check form-switch d-flex align-items-center mx-2 my-0" hidden={bs.ScreenUtil.isMobileScreen()}>
              <input className="form-check-input mt-0 me-2" type="checkbox"
                role="switch" id="editableToggle" checked={this.filter.enableJobStep}
                onChange={() => {
                  this.onModify(this.filter, 'enableJobStep', this.filter.enableJobStep, !this.filter.enableJobStep);
                }} />
              <label className="form-check-label fs--1 mb-0 d-flex align-items-center" htmlFor="editableToggle" >
                <span>{T('Job Step')}</span>
              </label>
            </div>

          </div>
        </div>

        <div className='flex-vbox' key={this.viewId}>
          <UIInquiryRequestTrackingList appContext={appContext} pageContext={pageContext}
            dialogEditor={false} editorTitle={''} ref={this.vgridRef}
            plugin={new entity.VGridEntityListEditorPlugin(this.records)} enableJobStep={this.filter.enableJobStep} />
        </div>
      </div>

    )
  }

}
