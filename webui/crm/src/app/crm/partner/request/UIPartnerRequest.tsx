
import React from 'react';
import * as FeatherIcon from "react-feather";
import { util, bs, input, entity } from "@datatp-ui/lib";
import { module } from '@datatp-ui/erp';

import BBRefLocation = module.settings.BBRefLocation;
import BBRefCountry = module.settings.BBRefCountry;
import BBRefState = module.settings.BBRefState;
import { BBRefCrmUserRole } from 'app/crm/common/template/BBRefCrmUserRole';

export const SOURCE_OPTIONS: string[] = [
  "WCA", "WPA", "GFFG", "CLC Projects",
  "A2B (not a member of associations in this list, Agent has one-way nominations to <PERSON>)",
  "B2A (not a member of associations in this list, <PERSON> has one-way nominations to Agent)",
  "A2B2A (not a member of associations in this list, Agent and Bee have reciprocal nominations)",
  "FREEHAND", "BEE", "MLN (Millenium Logistics Network)",
  "FREIGHT MIDPOINT", "LOGNET", "GAA", "PCN", "CFN",
  "EAA", "DCS", "ISS", "FCUBE", "TALA",
  "FPS - FAMOUS PACIFIC SHIPPING", "JOINT SALES", "PANGEA NETWORK"
]

function validatePartner(partner: any) {
  let agentCategories: string[] = ['AGENT_OVERSEAS', 'AGENT_DOMESTIC']

  if (agentCategories.includes(partner['category'])) return;
  let missingFields: string[] = [];

  if (!partner['label'] || !partner['localizedLabel']) missingFields.push('Partner Name (En/Vn)');
  if (!partner['address'] || !partner['localizedAddress']) missingFields.push('Address (En/Vn)');
  if (partner['category'] != 'AGENT_DOMESTIC' && partner['category'] != 'AGENT_OVERSEAS' && !partner['refund'] && !partner['provinceId']) missingFields.push('Province');
  if (!partner['countryId']) missingFields.push('Country');
  if (!partner['personalContact']) missingFields.push('Personal Contact');
  if (!partner['cell']) missingFields.push('Cell Phone');
  if (!partner['email']) missingFields.push('Email');
  if (!partner['industryCode'] || !partner['industryLabel']) missingFields.push('Industry');

  if (missingFields.length > 0) {
    bs.dialogShow('Missing Information',
      <div className="text-danger fw-bold text-center py-3 border-bottom">
        <FeatherIcon.AlertCircle className="mx-2" />
        {`Please provide: ${missingFields.join(', ')}.`}
      </div>,
      { backdrop: 'static', size: 'sm' }
    );
    throw new Error(`Please provide: ${missingFields.join(', ')}.`);
  }
}


export class UIPartnerEditor extends entity.AppDbEntityEditor {
  onCheckTaxCode = (_wInput: input.WInput, _bean: any, _field: string, _oldVal: any, newVal: any) => {
    const { appContext } = this.props;
    appContext.createHttpBackendCall('CRMPartnerService', 'findByTaxCode', { taxCode: newVal })
      .withSuccessData((partnerList: any[]) => {
        if (partnerList.length > 0) {
          let message = (
            <div className="ms-1 text-warning py-3 border-bottom">
              A partner with the tax code "{newVal}" already exists in the system.
            </div>
          );
          bs.dialogShow('Invalid Tax Code', message, { backdrop: 'static', size: 'sm' });
        }
      })
      .call();
  }

  onUpdateSimilarFields = (bean: any, field: string, _oldVal: any, newVal: any) => {
    bean[field] = newVal;
    if (field === 'name') {
      if (!bean['label'] || bean['label'].length == 0) bean['label'] = newVal;
      if (!bean['localizedLabel'] || bean['localizedLabel'].length == 0) bean['localizedLabel'] = newVal;
    }
    if (field === 'address') {
      if (!bean['localizedAddress'] || bean['localizedAddress'].length == 0) bean['localizedAddress'] = newVal;
    }
    this.forceUpdate();
  }

  onPreCommit = (observer: entity.BeanObserver) => {
    let partner = observer.getMutableBean();
    validatePartner(partner);
  }

  render() {
    let { appContext, pageContext, observer } = this.props;
    let partner = observer.getMutableBean();
    let categories = ['CUSTOMER', 'COLOADER', 'SHIPPER', 'CONSIGNEE'];

    return (
      <div className="flex-vbox">
        <bs.Row>
          <bs.Col span={3}>
            <input.BBStringField
              bean={partner} field='partnerCode' label={'Partner No.'} disable />
          </bs.Col>
          <bs.Col span={3}>
            <input.BBSelectField
              bean={partner} field='category' label={'Category'}
              options={categories} />
          </bs.Col>

          <bs.Col span={3}>
            <input.BBStringField
              bean={partner} field='taxCode' label={'Tax Code'} required
              onBgInputChange={this.onCheckTaxCode} />
          </bs.Col>
          <bs.Col span={3}>
            <BBRefCrmUserRole minWidth={500} hideMoreInfo label={'Saleman'}
              appContext={appContext} pageContext={pageContext} bean={partner}
              beanIdField='requestSalemanAccountId' beanLabelField='requestSalemanLabel' placeholder='Select Saleman Request...'
              onPostUpdate={(_inputUI: React.Component, bean: any, selectOpt: any, userInput: string) => {
                bean['requestSalemanAccountId'] = selectOpt['accountId'];
                bean['requestSalemanLabel'] = selectOpt['fullName'];
                this.forceUpdate();
              }} />
          </bs.Col>
        </bs.Row>
        <bs.Row>
          <bs.Col span={12}>
            <input.BBStringField
              bean={partner} field='name' label={'Partner Name (Abb)'} required
              onInputChange={this.onUpdateSimilarFields} />
          </bs.Col>
        </bs.Row>

        <bs.Row>
          <bs.Col span={12}>
            <input.BBStringField
              bean={partner} field='label' label={'Partner Name (En)'} />
          </bs.Col>
        </bs.Row>

        <bs.Row>
          <bs.Col span={12}>
            <input.BBStringField
              bean={partner} field='localizedLabel' label={'Partner Name (VN)'} />
          </bs.Col>
        </bs.Row>

        <bs.Row>
          <bs.Col span={3}>
            <BBRefCountry key={util.IDTracker.next()}
              appContext={appContext} pageContext={pageContext}
              placement="bottom-start" offset={[0, 5]} minWidth={350}
              label={'Country'} placeholder="Enter Country"
              required bean={partner} beanIdField={'countryId'} hideMoreInfo
              beanLabelField={'countryLabel'} refCountryBy='id'
              onPostUpdate={(_inputUI: React.Component, bean: any, selectOpt: any, userInput: string) => {
                bean['countryId'] = selectOpt['id'];
                bean['countryLabel'] = selectOpt['label'];
                this.forceUpdate();
              }}
            />
          </bs.Col>

          <bs.Col span={3}>
            <BBRefState key={util.IDTracker.next()}
              appContext={appContext} pageContext={pageContext}
              placement="bottom-start" offset={[0, 5]} minWidth={350}
              label={'Province'} placeholder="Enter Province"
              required bean={partner} beanIdField={'provinceId'} hideMoreInfo
              beanLabelField={'provinceLabel'} countryId={partner.countryId}
            />
          </bs.Col>
          <bs.Col span={3}>
            <BBRefLocation label='KCN'
              appContext={appContext} pageContext={pageContext} bean={partner}
              beanIdField={'kcnCode'} beanLabelField={'kcnLabel'} placeholder='KCN' hideMoreInfo
              inputObserver={observer} refLocationBy='id' locationTypes={['KCN']}
              beanRefLabelField='label'
              onPostUpdate={(_inputUI: React.Component, bean: any, selectOpt: any, userInput: string) => {
                if (selectOpt && selectOpt['id']) {
                  bean['countryId'] = selectOpt['countryId'];
                  bean['countryLabel'] = selectOpt['countryLabel'];
                  bean['provinceId'] = selectOpt['stateId'];
                  bean['provinceLabel'] = selectOpt['stateLabel'];
                  bean['kcnLabel'] = selectOpt['label'];
                  bean['address'] = selectOpt['address'];
                  this.forceUpdate();
                }
              }} />
          </bs.Col>
          <bs.Col span={3}>
            <input.BBStringField bean={partner} label={'Investment Origin'} field="investmentOrigin" />
          </bs.Col>
        </bs.Row>

        <bs.Row>
          <bs.Col span={6}>
            <input.BBTextField
              bean={partner} label={'Address (En)'} field="address" required
              onInputChange={this.onUpdateSimilarFields} style={{ height: '4em' }} />
          </bs.Col>
          <bs.Col span={6}>
            <input.BBTextField
              bean={partner} label={'Address (Vn)'} field="localizedAddress" required
              style={{ height: '4em' }} />
          </bs.Col>
        </bs.Row>

        <bs.Row>
          <bs.Col span={3}>
            <input.BBStringField
              bean={partner} field='personalContact' label={'Personal Contact'} required />
          </bs.Col>
          <bs.Col span={3}>
            <input.BBStringField
              bean={partner} field='cell' label={'Cell Phone'} required />
          </bs.Col>
          <bs.Col span={3}>
            <input.BBStringField
              bean={partner} field='fax' label={'FAX.'} />
          </bs.Col>
          <bs.Col span={3}>
            <input.BBStringField
              bean={partner} field='email' label={'Email'} required validators={[util.validator.EMAIL_VALIDATOR]} />
          </bs.Col>
        </bs.Row>

        <bs.Row>
          <bs.Col span={3}>
            <input.BBSelectField bean={partner} field="source" label={'Source'}
              options={SOURCE_OPTIONS} />
          </bs.Col>

          <bs.Col span={3}>
            <input.BBStringField
              bean={partner} field='swiftCode' label={'Swift Code.'} />
          </bs.Col>
          <bs.Col span={3}>
            <input.BBStringField
              bean={partner} field='bankName' label={'Bank Name.'} />
          </bs.Col>

          <bs.Col span={3}>
            <input.BBStringField
              bean={partner} field='bankAddress' label={'Bank Address.'} />
          </bs.Col>
        </bs.Row>

        <bs.Row>

          <bs.Col span={3}>
            <input.BBSelectField bean={partner} field="groupName" label={'Partner Group Type'}
              options={['NORMAL', 'FACTORY', 'CO-LOADER', 'OTHERS']} />
          </bs.Col>

          <bs.Col span={3}>
            <module.resource.BBRefResource
              key={`new-partner-industry-${this.viewId}`}
              appContext={appContext} pageContext={pageContext}
              placement="bottom-start" offset={[0, 5]} minWidth={350}
              label={'Industry'} placeholder="Enter Industry"
              required bean={partner} beanIdField={'industryCode'} hideMoreInfo
              beanLabelField={'industryLabel'} resourceType={"industry"} refResourceBy="identifier" />
          </bs.Col>
          <bs.Col span={3}>
            <input.BBStringField
              bean={partner} field='routing' label={'Route'} placeholder='Enter Route'
            />
          </bs.Col>
          <bs.Col span={3}>
            <input.BBSelectField
              bean={partner} field='scope' label={'Location'}
              options={['Domestic', 'Overseas']}
            />
          </bs.Col>
        </bs.Row>

        <input.BBTextField
          bean={partner} label={'Note'} field="note"
          style={{ height: '8em', fontSize: '1rem' }} />
        <bs.Toolbar className='border'>

          <entity.ButtonEntityCommit
            appContext={appContext} pageContext={pageContext} observer={observer}
            commit={{
              entityLabel: 'Customer', context: 'company',
              service: "CRMPartnerService", commitMethod: "saveCRMPartner"
            }}
            onPreCommit={this.onPreCommit} onPostCommit={this.onPostCommit} />

        </bs.Toolbar>
      </div>
    )
  }
}