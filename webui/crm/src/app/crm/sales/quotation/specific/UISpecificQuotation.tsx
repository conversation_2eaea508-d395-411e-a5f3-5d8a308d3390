import React from 'react';
import * as FeatherIcon from 'react-feather';
import { bs, server, util, entity, app } from '@datatp-ui/lib';

import { SalesTaskStatus, T } from '../../backend'
import { UIBookingUtils } from '../../booking/BookingUtils';
import { UIQuotationUtils } from '../QuotationUtils';

import {
  CustomerEntityUtil,
  UIQuotationListEditor,
} from '../../common';
import { UISpecificInquiryForm } from '../../inquiry';

import {
  CalculatorContext,
  CalculatorContextType,
  CalculatorProvider,
  createCalculatorContext,
} from '../../calculator';

import { SaleTaskType } from "../../backend";

import { SQuotationExportProcessor } from './xlsx/UIQuotationExportUtil';
import { DOMESTIC_CHARGES, ExportQuotationConfigDialog } from '../QuotationConfigDialog';
import { UICustomerLocalChargeList } from '../../common/UICustomerLocalChargeList';
import { ContainerType, ContainerTypeUnit } from 'app/crm/common/ContainerTypeUtil';
import { UIMailRequestPricing } from 'app/crm/price';

import {
  mapToTypeOfShipment,
  TransportationMode, TransportationTool
} from 'app/crm/common';
import { UISalesDailyTaskEditor } from '../../report/UIDailyTaskEditor';

const SESSION = app.host.DATATP_SESSION;

export class UISpecificQuotationEditor extends entity.AppDbComplexEntityEditor {
  isModifying: boolean = false;
  mainViewId = `view-${util.IDTracker.next()}`;
  scrollContainerRef: React.RefObject<HTMLDivElement> = React.createRef();

  getScrollContainerHeight = (): number => {
    return this.scrollContainerRef.current?.clientHeight || 0;
  }

  onNewBooking = () => {
    let { observer, appContext, pageContext } = this.props;
    let quotation = observer.getMutableBean();

    this.onPreCommit(observer)
    let quoteList: any[] = quotation['quoteListSelector'] || [];

    console.log(quotation);

    let committedEntityCallback = (entity: any) => {
      appContext.addOSNotification('success', "Auto save quotation!!!");
      entity['quoteListSelector'] = quoteList;
      observer.replaceWith(entity);
      observer.commitAndGet();
      this.onPostCommit(entity);
      if (pageContext.breadcumbs) pageContext.breadcumbs.forceUpdatePreviousPage();
      UIBookingUtils.onNewBooking(appContext, pageContext, quotation);
    };

    appContext
      .createHttpBackendCall('QuotationService', 'saveSpecificQuotation', { entity: quotation })
      .withSuccessData(committedEntityCallback)
      .call()
  }

  onNewQuotationTask = (inquiry: any) => {
    let { pageContext } = this.props;
    let serviceType = mapToTypeOfShipment(inquiry['purpose'], inquiry['mode']);
    let cargoReadyDate = util.text.formater.compactDate(inquiry['cargoReadyDate']);

    let finalDestination = inquiry['finalDestination'];
    let volume = '';
    if (TransportationTool.isSeaFCL(inquiry['mode'])) {
      volume = inquiry['containerTypes'] || '';
    } else if (TransportationTool.isSeaLCL(inquiry['mode'])) {
      volume = `${inquiry['volumeCbm'] || 0} CBM`;
    } else if (TransportationTool.isAir(inquiry['mode'])) {
      volume = `${inquiry['chargeableWeight'] || 0}`;
    }
    let descOfGoods = inquiry['descOfGoods'];

    let description = "Service Type: " + serviceType + " / " + inquiry['incoterms'] + "\n";
    description += "CRD: " + cargoReadyDate + "\n";
    description += "POL: " + inquiry['fromLocationLabel'] + "\n";
    description += "POD: " + inquiry['toLocationLabel'] + "\n";

    if (finalDestination) {
      description += "Final Dest: " + finalDestination + "\n";
    }

    description += "Vol: " + volume;

    if (descOfGoods) {
      description += "\nCommodity: " + descOfGoods;
    }

    let data = {
      taskType: SaleTaskType.QUOTATION.value,
      status: SalesTaskStatus.COMPLETED.value,
      taskName: T('Quotation Task'),
      partnerId: inquiry['clientPartnerId'],
      partnerLabel: inquiry['clientLabel'],
      createdDate: util.TimeUtil.javaCompactDateTimeFormat(new Date()),
      dueDate: util.TimeUtil.javaCompactDateTimeFormat(new Date()),
      salemanAccountId: SESSION.getAccountId(),
      salemanLabel: SESSION.getAccountAcl().getFullName(),
      description: description,
    }

    let observer = new entity.BeanObserver(data);

    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      const onPostCommit = (_entity: any, _uiEditor?: bs.BaseComponent) => {
        pageCtx.back();
      }
      return (
        <UISalesDailyTaskEditor
          appContext={appCtx} pageContext={pageCtx} observer={observer} onPostCommit={onPostCommit} />
      )
    }
    let popupId = `new-quotation-task-${util.IDTracker.next()}`;
    pageContext.createPopupPage(popupId, 'New Quotation Task', createAppPage, { size: 'flex-lg', backdrop: 'static' });
  }

  onModify = (_quotation: any, field: string, oldVal: any, newVal: any) => {
    let { observer } = this.props;
    if (field.length > 0) {
      observer.replaceBeanProperty(field, newVal);
    } else {
      observer.setMutableBean(_quotation);
    }
    this.isModifying = true;
    observer.commitAndGet();
    this.nextViewId()
    this.forceUpdate();
  }

  onSendQuotation = () => {
    let { appContext } = this.props;
    appContext.addOSNotification('warning', "Feature coming soon...");
    return;
  }

  doExportDateTemplate = () => {
    const { observer, appContext } = this.props;
    const quoteList: any[] = observer.getComplexArrayProperty('quoteList', []);

    if (!quoteList || quoteList.length === 0) {
      bs.dialogShow('Message',
        <div className="text-danger text-center p-2">
          Please select at least one price to export!
        </div>
      );
      return;
    }

    const quotation: any = observer.getMutableBean();

    let exportModel = UIQuotationUtils.doExportQuotationTemplate(quotation);
    appContext.createHttpBackendCall('DataMappingService', 'xlsxPrivateExport', { model: exportModel })
      .withSuccessData((data: any) => {
        let storeInfo = data;
        if (storeInfo.type == 'ir.actions.act_url') {
          entity.StoreInfo.download(storeInfo.url);
        } else if (storeInfo.privateMode) {
          entity.StoreInfo.privateDownload(storeInfo);
        } else {
          entity.StoreInfo.download(storeInfo.url);
        }
      })
      .call();
  };

  doExportXlsxQuotation = (clientType: 'CUSTOMER' | 'AGENT' = 'CUSTOMER') => {
    let { observer } = this.props;
    observer.updateMutableBean();
    let quoteSelector: any[] = observer.getComplexArrayProperty('quoteListSelector', [])
    let quotation: any = observer.getMutableBean();
    let inquiry: any = observer.getComplexBeanProperty('inquiry', {});
    let finalDestination = inquiry['finalDestination'] || '';

    if (quoteSelector.length === 0) {
      quotation['quoteListSelector'] = quotation['quoteList']
      quoteSelector = quotation['quoteList']
    }

    const matchedRoute: boolean = finalDestination ? false : true;

    let processor = new SQuotationExportProcessor(quotation, !matchedRoute, clientType);
    UIQuotationUtils.doExportQuoteAsXlsx(this, processor.request);
  }

  onModifyInquiry = (inquiry: any, field: string, _oldVal: any, _newVal: any) => {
    const { observer } = this.props;

    const inquiryObserver = observer.createComplexBeanObserver('inquiry', {});
    if (field.length > 0) {
      inquiryObserver.replaceBeanProperty(field, _newVal);
    } else {
      inquiryObserver.setMutableBean(inquiry);
    }
    const updatedInquiry = inquiryObserver.updateMutableBean();
    observer.replaceBeanProperty('inquiry', updatedInquiry);

    //TODO: Dan - Handle weight/volume updates
    const volumeFields: string[] = ['chargeableVolume', 'volumeCbm', 'chargeableWeight', 'grossWeightKg'];
    if (volumeFields.includes(field)) {

      const volume = field === 'chargeableVolume' ?
        updatedInquiry['chargeableVolume'] :
        (updatedInquiry['volumeCbm'] || 0);

      const weight = field === 'chargeableWeight' ?
        updatedInquiry['chargeableWeight'] :
        updatedInquiry['grossWeightKg'];

      let exitsLocationCharges: any[] = observer.getComplexArrayProperty('localHandlingCharges', []);

      let updateLocalCharges: any[] = [];
      for (let localCharge of exitsLocationCharges) {
        if (localCharge['unit'] === 'CBM') {
          localCharge['quantity'] = volume;
          if (localCharge['unit'] === 'KGS' || localCharge['unit'] === 'KGM' || localCharge['unit'] === 'KG') {
            localCharge['quantity'] = weight;
          } else {
            localCharge['quantity'] = 1;
          }
        }
        updateLocalCharges.push(localCharge);
      }
      observer.replaceBeanProperty('localHandlingCharges', updateLocalCharges);
    } else if (field === 'incoterms') {
      let termOfService: string = inquiry['termOfService'];
      if (termOfService === 'DOOR_TO_DOOR' || termOfService === 'DOOR_TO_PORT' || termOfService === 'PORT_TO_DOOR') {
        let exitsLocationCharges: any[] = observer.getComplexArrayProperty('localHandlingCharges', []);
        let domesticsLocationCharges = DOMESTIC_CHARGES.map(charge => ({
          ...charge,
          target: termOfService === 'PORT_TO_DOOR' ? 'DESTINATION' : 'ORIGIN',
          currency: 'USD',
          quantity: '',
          unitPrice: '',
          totalAmount: '',
          note: '',
          quoteRate: {}
        }));
        observer.replaceBeanProperty('localHandlingCharges', [...exitsLocationCharges, ...domesticsLocationCharges]);
      }
    }

    let chargeModel = observer.getComplexBeanProperty('customerChargeModel', {});
    if (!chargeModel || Object.keys(chargeModel).length === 0) {
      chargeModel = CustomerEntityUtil.createDefaultCustomerChargeModel();
    }
    this.isModifying = true;
    this.nextViewId();
    this.forceUpdate();
  }

  copyQuotation = () => {
    const { appContext, observer } = this.props;
    let quotation = observer.getMutableBean();

    appContext.addOSNotification('info', "Expired freight rates will be ignored");

    appContext.createHttpBackendCall('QuotationService', 'copySpecificQuotation', { quotationId: quotation['id'] })
      .withSuccessData((data: any) => {
        appContext.addOSNotification('success', "Copy quotation success!!!");
        UIQuotationUtils.showUISpecificQuotation(this, data);
      })
      .withFail((response: server.BackendResponse) => {
        let title = T('Copy Quotation Failed!');
        appContext.addOSNotification('danger', title, response.error, response);
        let message = response.error.message || title;
        bs.notificationShow("danger", title, <div className='text-danger'>{message}</div>);
        return;
      })
      .call();
  }

  onPricingRequest = () => {
    const { pageContext, observer } = this.props;
    let inquiry = observer.getComplexBeanProperty('inquiry', {});

    let requestModel = {
      mode: inquiry.mode,
      purpose: inquiry.purpose,
      fromLocationCode: inquiry.fromLocationCode,
      fromLocationLabel: inquiry.fromLocationLabel,
      toLocationCode: inquiry.toLocationCode,
      toLocationLabel: inquiry.toLocationLabel,
      cargoReadyDate: inquiry.cargoReadyDate || util.TimeUtil.javaCompactDateTimeFormat(new Date()),
      estimatedTimeDeparture: inquiry.estimatedTimeDeparture,
      termOfService: inquiry.termOfService,
      incoterms: inquiry.incoterms,
      containerTypes: inquiry.containerTypes,
      shipmentDetail: {
        commodity: inquiry.commodity || 'GENERAL',
        volumeInfo: inquiry.containerTypes,
        grossWeightKg: inquiry.grossWeightKg || 0,
        volumeCbm: inquiry.volumeCbm || 0,
        packageQty: inquiry.packageQty || 0,
        descOfGoods: inquiry.descOfGoods || ''
      },
      containers: inquiry.containers || []
    };

    const createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => (
      <div className='flex-hbox'>
        <UIMailRequestPricing appContext={appCtx} pageContext={pageCtx}
          observer={new entity.ComplexBeanObserver(requestModel)}
          onPostCommit={(_entity) => { pageCtx.back(); }}
        />
      </div>
    );

    let type: string = mapToTypeOfShipment(requestModel.purpose, requestModel.mode) || 'N/A';

    let size: 'flex-lg' | 'xl' = 'flex-lg';
    if (window.innerWidth < 1300) {
      size = 'xl';
    }

    pageContext.createPopupPage('mail-request-pricing', `Request Pricing(${type})`, createPageContent, {
      size: size,
      backdrop: 'static'
    });
  }

  onPreCommit = (observer: entity.ComplexBeanObserver) => {
    if (!observer.getErrorCollector().assertNoError('Quotation Has Error')) return;
    observer.updateMutableBean();

    let quotation = observer.getMutableBean();

    let quoteList = quotation['quoteList'];
    // Check all quoteList records for fromLocationCode and toLocationCode
    // if (Array.isArray(quoteList)) {
    //   for (let i = 0; i < quoteList.length; i++) {
    //     const quote = quoteList[i];
    //     if (!quote.fromLocationCode || !quote.toLocationCode) {
    //       bs.dialogShow('Error',
    //         <div className="text-danger fw-bold text-center py-3 border-bottom">
    //           <FeatherIcon.AlertCircle className="mx-2" />
    //           {T('Please provide both Port Of Loading and Port Of Discharge for all quotes.')}
    //           <br />
    //           {T(`(row ${i + 1})`)}
    //         </div>,
    //         { backdrop: 'static', size: 'md' }
    //       );
    //       throw new Error(`Please provide both Port Of Loading and Port Of Discharge for all quotes (row ${i + 1}).`);
    //     }
    //   }
    // }

    let inquiry = observer.getComplexBeanProperty('inquiry', {});

    if (!inquiry['fromLocationCode'] || !inquiry['toLocationCode']) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please provide both Port Of Loading and Port Of Discharge .')}
        </div>,
        { backdrop: 'static', size: 'md' }
      );
      throw new Error('Please provide both Port Of Loading and Port Of Discharge.')
    }

    let gw: number = inquiry['grossWeightKg'] || 0;
    let cbm: number = inquiry['volumeCbm'] || 0;
    let containerTypes: string = inquiry['containerTypes'] || '';
    let mode: TransportationMode = inquiry['mode'];

    if (TransportationTool.isSeaFCL(mode) && !containerTypes) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please provide Container Types.')}
        </div>,
        { backdrop: 'static', size: 'sm' }
      );
      throw new Error('Please provide Container Types.')
    } else if (TransportationTool.isAir(mode) || TransportationTool.isSeaLCL(mode)) {
      if (gw + cbm === 0) {
        bs.dialogShow('Error',
          <div className="text-danger fw-bold text-center py-3 border-bottom">
            <FeatherIcon.AlertCircle className="mx-2" />{T('Please provide GrossWeight/ CBM.')}
          </div>,
          { backdrop: 'static', size: 'sm' }
        );
        throw new Error('Please provide GrossWeight/ CBM.')
      }
    }

  }

  onPostCommit = (entity: any) => {
    let { onPostCommit } = this.props;
    this.nextViewId();
    this.isModifying = false;
    if (onPostCommit) {
      onPostCommit(entity, this);
    } else {
      this.forceUpdate();
    }
  }

  onExportQuotation = () => {
    ExportQuotationConfigDialog(this, (_quotation: any) => { }, (_quotation: any) => { })
  }

  renderQuoteList(columnH: number) {
    let { appContext, pageContext, observer } = this.props;
    let inquiry: any = observer.getComplexBeanProperty('inquiry', {});
    let mode: TransportationMode = inquiry['mode'];

    let localCharges: any[] = observer.getComplexArrayProperty('localHandlingCharges', []);

    // Sort local charges theo priority
    localCharges.sort((a, b) => {
      // Priority cuối cùng: S_TRUCK luôn ở cuối danh sách
      const aIsTruck = (a.code || '').trim() === 'S_TRUCK';
      const bIsTruck = (b.code || '').trim() === 'S_TRUCK';

      if (aIsTruck && !bIsTruck) return 1;
      if (!aIsTruck && bIsTruck) return -1;

      // Priority cuối 2: S_CUSTOM1 ở cuối nhưng trước S_TRUCK
      const aIsCustom1 = (a.code || '').trim() === 'S_CUSTOM1';
      const bIsCustom1 = (b.code || '').trim() === 'S_CUSTOM1';

      if (aIsCustom1 && !bIsCustom1) return 1;
      if (!aIsCustom1 && bIsCustom1) return -1;

      // Giữ nguyên thứ tự ban đầu cho các items khác
      return 0;
    });

    let containers: ContainerType[] = ContainerTypeUnit.computeFromContainer(inquiry['containers']) || []
    if (containers.length === 0) {
      ContainerTypeUnit.parseContainerString(inquiry['containerTypes']).map(container => {
        const type: ContainerType | undefined = ContainerTypeUnit.match(container.containerType);
        if (type) containers.push(type);
      });
    }

    const onModifyLocalCharges = (records: Array<any>, _action: any) => {
      for (let record of records) {
        if (!record['mode']) {
          record['mode'] = mode;
        }
      }
      observer.updateMutableBean();
      // observer.replaceBeanProperty('localHandlingCharges', records);
    }

    const onModifyQuoteList = (records: Array<any>, _action: any) => {
      // observer.replaceBeanProperty('quoteList', records);
      observer.updateMutableBean();
    }

    let localChargePlugin = observer.createVGridEntityListEditorPlugin('localHandlingCharges', []);

    let quoteListPlugin = observer.createVGridEntityListEditorPlugin('quoteList', []);

    if (TransportationTool.isSea(mode) || TransportationTool.isAir(mode)) {
      return (
        <div className='flex-vbox'>
          <div className='flex-vbox flex-grow-0' style={{ height: 350 }}>
            <UIQuotationListEditor
              appContext={appContext} pageContext={pageContext} observer={observer} initContainers={containers}
              dialogEditor={false} editorTitle={''} plugin={quoteListPlugin} onModifyBean={onModifyQuoteList} />
          </div>
          <div className='flex-vbox'>
            <UICustomerLocalChargeList dialogEditor={false} editorTitle={''}
              appContext={appContext} pageContext={pageContext} typeOfContainers={containers}
              plugin={localChargePlugin} onModifyBean={onModifyLocalCharges} />
          </div>
        </div>
      )
    } else {
      return (
        <UICustomerLocalChargeList dialogEditor={false} editorTitle={''}
          appContext={appContext} pageContext={pageContext} typeOfContainers={containers}
          plugin={localChargePlugin} onModifyBean={onModifyLocalCharges} />
      )
    }
  }

  render() {
    let { appContext, pageContext, observer } = this.props;
    const writeCap = pageContext.hasUserWriteCapability();
    let inquiryObserver = observer.createComplexBeanObserver('inquiry', {})
    const quoteContainerH = this.getScrollContainerHeight();
    const columnH = Math.floor(quoteContainerH / 3);
    let minColumnH = Math.max(columnH, 350)

    return (
      <div className='flex-vbox px-1 my-1 mx-0' key={this.mainViewId}>
        <bs.VSplit updateOnResize>
          <bs.VSplitPane width={380} title={T('Inquiry')} >
            <UISpecificInquiryForm key={`inquiry-${this.viewId}`}
              appContext={appContext} pageContext={pageContext} observer={inquiryObserver} readOnly={!writeCap}
              onModify={this.onModifyInquiry} />
          </bs.VSplitPane>
          <bs.VSplitPane title={T('Price Quote')} >
            <div className='flex-vbox' key={`quote-${this.viewId}`} ref={this.scrollContainerRef}>
              {this.renderQuoteList(minColumnH)}
            </div>
          </bs.VSplitPane>
        </bs.VSplit>

        <bs.Toolbar className='border gap-1'>

          <bs.Button laf='primary' className='flex-hbox-grow-0 justify-content-center align-items-center'
            hide={observer.isNewBean()} onClick={() => this.onNewQuotationTask(observer.getComplexBeanProperty('inquiry', {}))}>
            <FeatherIcon.Plus className='me-1' size={12} /> {T('Quotation Task')}
          </bs.Button>

          <bs.Button laf='warning' className="border-0 p-1" style={{ width: '150px' }}
            onClick={() => this.onPricingRequest()}>
            <FeatherIcon.Mail size={12} /> Request Pricing
          </bs.Button>

          <bs.Button laf='info' className='d-flex align-items-center justify-content-start px-1 py-2'
            hide={observer.isNewBean()}
            onClick={() => this.doExportDateTemplate()}>
            <FeatherIcon.Users size={14} className="me-2" /> {T('Data Template')}
          </bs.Button>

          <bs.Button laf='primary' className='flex-hbox-grow-0 justify-content-center align-items-center'
            hide={observer.isNewBean()} onClick={this.onExportQuotation}>
            <FeatherIcon.Download className='me-1' size={12} /> {T('Export')}
          </bs.Button>

          <bs.Button laf='primary' className='flex-hbox-grow-0 justify-content-center align-items-center'
            // hide={observer.isNewBean()} onClick={this.onNewBooking} disabled={editMode !== 'DRAFT'}>
            hide={observer.isNewBean()} onClick={this.onNewBooking}>
            <FeatherIcon.Plus className='me-1' size={12} /> {T('Internal Booking')}
          </bs.Button>

          <bs.Button laf='info' className='flex-hbox-grow-0'
            hide={observer.isNewBean()} onClick={this.copyQuotation} >
            <FeatherIcon.Copy size={14} className='me-2' /> {T('Save As')}
          </bs.Button>

          <entity.ButtonEntityCommit
            appContext={appContext} pageContext={pageContext}
            hide={!writeCap} observer={observer}
            commit={{
              entityLabel: T('Specific Quotation'),
              context: 'company',
              service: 'QuotationService', commitMethod: 'saveSpecificQuotation'
            }}
            onPostCommit={this.onPostCommit}
            onPreCommit={this.onPreCommit} />

        </bs.Toolbar>
      </div>
    );
  }
}

UISpecificQuotationEditor.contextType = CalculatorContext;

export class UISpecificQuotation extends entity.AppDbComplexEntityEditor {

  render(): React.ReactNode {
    let { observer } = this.props;
    let inquiry = observer.getComplexBeanProperty('inquiry', {});
    let chargeMargin = CustomerEntityUtil.createDefaultCustomerChargeModel();
    let calContext = createCalculatorContext(chargeMargin.transportChargeMargin, inquiry);

    return (
      <CalculatorProvider initContext={calContext}>
        <UISpecificQuotationEditor {...this.props} />
      </CalculatorProvider>
    )
  }

}
