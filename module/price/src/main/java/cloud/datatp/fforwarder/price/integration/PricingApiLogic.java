package cloud.datatp.fforwarder.price.integration;

import cloud.datatp.fforwarder.core.common.Purpose;
import cloud.datatp.fforwarder.core.db.CRMDaoService;

import java.util.Date;
import java.util.List;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.query.RangeFilter;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.security.client.ClientContext;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;
import org.springframework.stereotype.Component;

@Slf4j
@Getter
@Component
public class PricingApiLogic extends CRMDaoService {

  public List<SqlMapRecord> searchSeaFcl(ClientContext client, Params.FilterPricingParams params) {
    try {
      final String fromDateStr = params.getFromDate();
      final String toDateStr = params.getToDate();
      SqlQueryParams sqlParams = new SqlQueryParams("*");

      RangeFilter rangeFilter = new RangeFilter("validFrom");
      if (StringUtil.isNotEmpty(fromDateStr)) {
        Date fromDate = DateUtil.parseCompactDate(fromDateStr);
        rangeFilter.setFromValue(DateUtil.asCompactDateTime(fromDate));
      }

      if (StringUtil.isNotEmpty(toDateStr)) {
        Date toDate = DateUtil.parseCompactDate(toDateStr);
        rangeFilter.setToValue(DateUtil.asCompactDateTime(toDate));
      }
      sqlParams.add(rangeFilter);

      Purpose purpose = params.getPurpose();
      if (purpose == null) return java.util.Collections.emptyList();
      sqlParams.addParam("purpose", purpose.toString());
      sqlParams.addParam("groupType", params.getGroupType());
      String fileName = "SearchSeaFCLExportPrice";
      if (Purpose.isImport(purpose)) {
        fileName = "SearchSeaFCLImportPrice";
      }
      String scriptDir = appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/fforwarder/price/groovy/PricingApiSql.groovy";
      return searchDbRecords(client, scriptDir, scriptFile, fileName, sqlParams);
    } catch (Exception e) {
      log.error("-------------------- Search Sea FCL Error -------------------------");
      log.error(e.getMessage());
      return java.util.Collections.emptyList();
    }
  }

  public List<SqlMapRecord> searchSeaLcl(ClientContext client, Params.FilterPricingParams params) {
    try {
      final String fromDateStr = params.getFromDate();
      final String toDateStr = params.getToDate();
      SqlQueryParams sqlParams = new SqlQueryParams("*");

      RangeFilter rangeFilter = new RangeFilter("validFrom");
      if (StringUtil.isNotEmpty(fromDateStr)) {
        Date fromDate = DateUtil.parseCompactDate(fromDateStr);
        rangeFilter.setFromValue(DateUtil.asCompactDateTime(fromDate));
      }

      if (StringUtil.isNotEmpty(toDateStr)) {
        Date toDate = DateUtil.parseCompactDate(toDateStr);
        rangeFilter.setToValue(DateUtil.asCompactDateTime(toDate));
      }
      sqlParams.add(rangeFilter);

      Purpose purpose = params.getPurpose();
      if (purpose == null) return java.util.Collections.emptyList();
      sqlParams.addParam("purpose", purpose.toString());
      String fileName = "SearchSeaLCLExportPrice";
      if (Purpose.isImport(params.getPurpose())) {
        fileName = "SearchSeaLCLImportPrice";
      }
      String scriptDir = appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/fforwarder/price/groovy/PricingApiSql.groovy";
      return searchDbRecords(client, scriptDir, scriptFile, fileName, sqlParams);
    } catch (Exception e) {
      log.error("-------------------- Search Sea LCL Error -------------------------");
      log.error(e.getMessage());
      return java.util.Collections.emptyList();
    }
  }

  public List<SqlMapRecord> searchAir(ClientContext client, Params.FilterPricingParams params) {
    try {
      final String fromDateStr = params.getFromDate();
      final String toDateStr = params.getToDate();
      SqlQueryParams sqlParams = new SqlQueryParams("*");

      RangeFilter rangeFilter = new RangeFilter("validFrom");
      if (StringUtil.isNotEmpty(fromDateStr)) {
        Date fromDate = DateUtil.parseCompactDate(fromDateStr);
        rangeFilter.setFromValue(DateUtil.asCompactDateTime(fromDate));
      }

      if (StringUtil.isNotEmpty(toDateStr)) {
        Date toDate = DateUtil.parseCompactDate(toDateStr);
        rangeFilter.setToValue(DateUtil.asCompactDateTime(toDate));
      }
      sqlParams.add(rangeFilter);

      Purpose purpose = params.getPurpose();
      if (purpose == null) return java.util.Collections.emptyList();
      sqlParams.addParam("purpose", purpose.toString());
      String fileName = "SearchAirExportTransportCharge";
      if (Purpose.isImport(params.getPurpose())) {
        fileName = "SearchAirImportTransportCharge";
      }
      String scriptDir = appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/fforwarder/price/groovy/PricingApiSql.groovy";
      return searchDbRecords(client, scriptDir, scriptFile, fileName, sqlParams);
    } catch (Exception e) {
      log.error("-------------------- Search Air Error -------------------------");
      log.error(e.getMessage());
      return java.util.Collections.emptyList();
    }
  }

  public List<SqlMapRecord> searchTrucking(ClientContext client, Params.FilterPricingParams params) {
    try {
      final String fromDateStr = params.getFromDate();
      final String toDateStr = params.getToDate();
      SqlQueryParams sqlParams = new SqlQueryParams("*");

      RangeFilter rangeFilter = new RangeFilter("validFrom");
      if (StringUtil.isNotEmpty(fromDateStr)) {
        Date fromDate = DateUtil.parseCompactDate(fromDateStr);
        rangeFilter.setFromValue(DateUtil.asCompactDateTime(fromDate));
      }

      if (StringUtil.isNotEmpty(toDateStr)) {
        Date toDate = DateUtil.parseCompactDate(toDateStr);
        rangeFilter.setToValue(DateUtil.asCompactDateTime(toDate));
      }
      sqlParams.add(rangeFilter);
      String fileName = "SearchTruckPrice";
      String scriptDir = appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/fforwarder/price/groovy/PricingApiSql.groovy";
      return searchDbRecords(client, scriptDir, scriptFile, fileName, sqlParams);
    } catch (Exception e) {
      log.error("-------------------- Search Trucking Error -------------------------");
      log.error(e.getMessage());
      return java.util.Collections.emptyList();
    }
  }

  public List<SqlMapRecord> searchContainer(ClientContext client, Params.FilterPricingParams params) {
    try {
      final String fromDateStr = params.getFromDate();
      final String toDateStr = params.getToDate();
      SqlQueryParams sqlParams = new SqlQueryParams("*");

      RangeFilter rangeFilter = new RangeFilter("validFrom");
      if (StringUtil.isNotEmpty(fromDateStr)) {
        Date fromDate = DateUtil.parseCompactDate(fromDateStr);
        rangeFilter.setFromValue(DateUtil.asCompactDateTime(fromDate));
      }

      if (StringUtil.isNotEmpty(toDateStr)) {
        Date toDate = DateUtil.parseCompactDate(toDateStr);
        rangeFilter.setToValue(DateUtil.asCompactDateTime(toDate));
      }
      sqlParams.add(rangeFilter);
      String fileName = "SearchContainerPrice";
      String scriptDir = appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/fforwarder/price/groovy/PricingApiSql.groovy";
      return searchDbRecords(client, scriptDir, scriptFile, fileName, sqlParams);
    } catch (Exception e) {
      log.error("-------------------- Search Container Error -------------------------");
      log.error(e.getMessage());
      return java.util.Collections.emptyList();
    }
  }

}