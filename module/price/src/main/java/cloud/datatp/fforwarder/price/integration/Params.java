package cloud.datatp.fforwarder.price.integration;

import cloud.datatp.fforwarder.core.common.Purpose;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

public class Params {

  @Getter @Setter
  @NoArgsConstructor
  static public class FilterPricingParams {
    private String groupType;
    private Purpose purpose;
    private String fromDate;
    private String toDate;
  }

}