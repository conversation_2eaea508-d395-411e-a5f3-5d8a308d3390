package cloud.datatp.fforwarder.core.groovy

import net.datatp.lib.executable.ExecutableContext
import net.datatp.lib.executable.Executor
import net.datatp.module.data.db.ExecutableSqlBuilder
import net.datatp.util.ds.MapObject
import org.springframework.context.ApplicationContext

class PartnerRequestSql extends Executor {
    public class SearchPartnerRequest extends ExecutableSqlBuilder {
        @Override
        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            MapObject sqlParams = ctx.getParam("sqlParams");

            String query = """
                SELECT 
                    r.id                        AS id,
                    r.partner_id                AS partner_id,
                    r.bfsone_partner_code_temp  AS bfsone_partner_code_temp,
                    r.partner_name              AS partner_name,
                    r.partner_label             AS partner_label,
                    r.status                    AS status,
                    r.request_date              AS request_date,
                    r.request_by_account_id     AS request_by_account_id,
                    r.request_by_label          AS request_by_label,
                    r.approved_by_account_id    AS approved_by_account_id,
                    r.approved_by_label         AS approved_by_label,
                    r.approved_date             AS approved_date,
                    r.approved_note             AS approved_note
                FROM lgc_forwarder_partner_request r
                WHERE
                  ${FILTER_BY_STORAGE_STATE('r', sqlParams)}
                  AND (
                    ('System' = :space AND r.status = 'NEW') OR 
                    ('Company' = :space AND r.status = 'NEW') OR
                    ('User' = :space AND r.request_by_account_id = :accessAccountId)
                  )
                ORDER BY r.request_date DESC
                ${MAX_RETURN(sqlParams)}  
            """;
            return query;
        }
    }

    public PartnerRequestSql() {
        register(new SearchPartnerRequest());
    }
}