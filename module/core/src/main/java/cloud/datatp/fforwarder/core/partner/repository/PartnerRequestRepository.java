package cloud.datatp.fforwarder.core.partner.repository;

import java.io.Serializable;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import cloud.datatp.fforwarder.core.partner.entity.PartnerRequest;

@Repository
public interface PartnerRequestRepository extends JpaRepository<PartnerRequest, Serializable> {

  @Query("SELECT r FROM PartnerRequest r WHERE r.partnerId = :partnerId")
  List<PartnerRequest> findByPartnerId(Long partnerId);

}