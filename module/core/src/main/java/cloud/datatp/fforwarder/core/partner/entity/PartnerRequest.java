package cloud.datatp.fforwarder.core.partner.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import java.util.Date;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.data.db.entity.PersistableEntity;
import net.datatp.util.text.DateUtil;

@Entity
@Table(name = PartnerRequest.TABLE_NAME,
  uniqueConstraints = {
    @UniqueConstraint(
      name = PartnerRequest.TABLE_NAME + "_partner_id",
      columnNames = {"partner_id"}
    ),
  },
  indexes = {
    @Index(
      name = PartnerRequest.TABLE_NAME + "_partner_id_idx",
      columnList = "partner_id"
    ),
    @Index(
      name = PartnerRequest.TABLE_NAME + "_request_by_account_idx",
      columnList = "request_by_account_id"
    ),
    @Index(
      name = PartnerRequest.TABLE_NAME + "_approved_by_account_idx",
      columnList = "approved_by_account_id"
    ),
  }
)
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor
@Getter
@Setter
public class PartnerRequest extends PersistableEntity<Long> {

  private static final long serialVersionUID = 1L;

  public static final String TABLE_NAME = "lgc_forwarder_partner_request";

  public enum PartnerRequestStatus {
    NEW, PENDING, APPROVED, REJECTED, CANCELLED;

    public static PartnerRequestStatus parse(String token) {
      if (token == null)
        return PENDING;
      try {
        return valueOf(token.trim().toUpperCase());
      } catch (IllegalArgumentException e) {
        return PENDING;
      }
    }

    public static boolean isCompleted(PartnerRequestStatus status) {
      return status == PartnerRequestStatus.APPROVED || status == PartnerRequestStatus.REJECTED
        || status == PartnerRequestStatus.CANCELLED;
    }
  }

  @Column(name = "partner_id")
  private Long partnerId;

  @Column(name = "bfsone_partner_code_temp")
  private String bfsonePartnerCodeTemp;

  @Column(name = "partner_name")
  private String partnerName;

  @Column(name = "partner_label")
  private String partnerLabel;

  @Enumerated(EnumType.STRING)
  private PartnerRequestStatus status = PartnerRequestStatus.NEW;

  @Column(name = "request_date")
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  private Date requestDate;

  @Column(name = "request_by_account_id")
  private Long requestByAccountId;

  @Column(name = "request_by_label")
  private String requestByLabel;

  // from setup
  @Column(name = "approved_by_account_id")
  private Long approvedByAccountId;

  @Column(name = "approved_by_label")
  private String approvedByLabel;

  @Column(name = "approved_date")
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  private Date approvedDate;

  @Column(name = "approved_note", length = 1024 * 4)
  private String approvedNote;

  // from setup
  @Column(name = "mail_to")
  private String to;

  // from setup
  @Column(name = "mail_cc")
  private String cc;

  public PartnerRequest(CRMPartner partner) {
    this.status = PartnerRequestStatus.NEW;
    this.partnerId = partner.getId();
    this.partnerName = partner.getName();
    this.partnerLabel = partner.getLabel();
    this.bfsonePartnerCodeTemp = partner.getPartnerCodeTemp();
    this.requestDate = new Date();
  }

  public PartnerRequest withRequestBy(Long accountId, String label) {
    this.requestByAccountId = accountId;
    this.requestByLabel = label;
    return this;
  }
  
  
  
  
  

}