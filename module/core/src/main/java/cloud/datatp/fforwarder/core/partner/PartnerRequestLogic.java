package cloud.datatp.fforwarder.core.partner;

import cloud.datatp.fforwarder.core.db.CRMDaoService;
import cloud.datatp.fforwarder.core.integration.BFSOneApi;
import cloud.datatp.fforwarder.core.partner.entity.BFSOnePartnerGroup;
import cloud.datatp.fforwarder.core.partner.entity.CRMPartner;
import cloud.datatp.fforwarder.core.partner.entity.PartnerRequest;
import cloud.datatp.fforwarder.core.partner.repository.PartnerRequestRepository;
import cloud.datatp.fforwarder.core.template.CRMUserRoleLogic;
import cloud.datatp.fforwarder.core.template.entity.CrmUserRole;

import java.util.List;

import lombok.extern.slf4j.Slf4j;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.security.client.ClientContext;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PartnerRequestLogic extends CRMDaoService {

  @Autowired
  private PartnerRequestRepository requestRepo;
  
  @Autowired
  private BFSOneApi bfsOneApi;
  
  @Autowired
  private CRMUserRoleLogic crmUserRoleLogic;
  
  @Autowired
  private CRMPartnerLogic partnerLogic;

  public PartnerRequest getById(ClientContext client, Long id) {
    return requestRepo.findById(id).get();
  }

  public List<PartnerRequest> findByPartnerId(ClientContext client, Long partnerId) {
    return requestRepo.findByPartnerId(partnerId);
  }

  public PartnerRequest savePartnerRequest(ClientContext client, PartnerRequest request) {
    request.set(client);
    return requestRepo.save(request);
  }
  
  public PartnerRequest updatePartnerRequestStatus(ClientContext client, PartnerRequest request) {
    CRMPartner partner = partnerLogic.getById(client, request.getPartnerId());
    Long approvedByAccountId = request.getApprovedByAccountId();
    if (approvedByAccountId == null) approvedByAccountId = client.getAccountId();
    CrmUserRole approvedBy = crmUserRoleLogic.getByAccountId(client, approvedByAccountId);
    Objects.assertNotNull(approvedBy, "CrmUserRole is not found by accountId = {}", approvedByAccountId);
    String approvedByBfsoneEmployeeCode = approvedBy.getBfsoneCode();
    String approvedByBfsoneUsername = approvedBy.getBfsoneUsername();
    String authenticate = bfsOneApi.authenticate(approvedByBfsoneEmployeeCode, approvedByBfsoneUsername);
    
    Long salemanAccountId = request.getRequestByAccountId();
    Objects.assertNotNull(salemanAccountId, "RequestedByAccountId must not be null!");
    CrmUserRole saleman = crmUserRoleLogic.getByAccountId(client, salemanAccountId);
    Objects.assertNotNull(saleman, "Saleman is not found, account id = " + salemanAccountId);
    String salemanBfsoneEmployeeCode = saleman.getBfsoneCode();
    String salemanBfsoneUsername = saleman.getBfsoneUsername();
    
    MapObject bfsOnePartner = partner.toBFSOnePartner();
    bfsOnePartner.put("RequestUser", salemanBfsoneUsername);
    bfsOnePartner.put("SalemanID", salemanBfsoneEmployeeCode);
    bfsOnePartner.put("Email_Request", saleman.getEmail());
    bfsOnePartner.put("isUnApproved", null);
    bfsOnePartner.put("ReasonUnApproved", request.getApprovedNote());
    log.info("------------------------Partner---------------------------\n");
    DataSerializer.JSON.dump(bfsOnePartner);
    log.info("--------------------------------------------------------\n");

    BFSOnePartnerGroup group = partner.getPartnerGroup();
    bfsOneApi.partnerApprove(group, authenticate, bfsOnePartner);
    
    return request;
  }
  
  public List<SqlMapRecord> searchPartnerRequests(ClientContext client, SqlQueryParams sqlParams) {
    sqlParams.addParam("accessAccountId", client.getAccountId());
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/core/groovy/PartnerRequestSql.groovy";
    return searchDbRecords(client, scriptDir, scriptFile, "SearchPartnerRequest", sqlParams);
  }

}